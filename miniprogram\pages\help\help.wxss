/* 基础样式 */
.container {
  padding: 30rpx;
  background-color: #f5f7fa;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  color: #333;
}

.faq-count {
  font-size: 28rpx;
  color: #666;
}

.faq-count-number {
  color: #3B82F6;
  font-weight: bold;
  margin-right: 8rpx;
}

.back-btn {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 0;
  background: none;
  color: #3B82F6;
  font-size: 28rpx;
}

.icon {
  margin-right: 16rpx;
}

.action-btn {
  transition: transform 0.2s ease;
}

.action-btn:active {
  transform: scale(1.05);
}

/* FAQ 风格 */
.faq-section {
  margin-bottom: 60rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.faq-item {
  background-color: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.faq-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.faq-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: none;
}

.faq-question {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.chevron {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.rotate {
  transform: rotate(180deg);
}

.faq-content {
  padding-top: 24rpx;
  font-size: 26rpx;
  color: #666;
}

.tip-info {
  color: #3B82F6;
  margin-top: 20rpx;
  display: flex;
  align-items: center;

}

.tip-icon {
  margin-right: 8rpx;
}

/* 联系方式卡片 */
.contact-section {
  margin-bottom: 40rpx;
}

.contact-cards {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.contact-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.phone {
  background-color: #BFDBFE;
}

.chat {
  background-color: #BBF7D0;
}

.email {
  background-color: #E9D5FF;
}

.contact-icon {
  font-size: 36rpx;
  color: #3B82F6;
}

.card-text {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.card-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* .card-body {
  padding-left: 110rpx;
} */

.card-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.contact-link {
  color: #818CF8;
  font-size: 28rpx;
}

.chat-button {
  background-color: #34D399;
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  display: inline-flex;
  align-items: center;
}

.button-icon {
  margin-right: 12rpx;
}

/* 反馈按钮 */
.feedback-button {
  width: 100%;
  background-color: #3B82F6;
  color: white;
  font-size: 30rpx;
  font-weight: 500;
  padding: 28rpx 0;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.feedback-icon {
  margin-right: 20rpx;
}