App({
  globalData: {
    userInfo: null,
    token: null,
    userID: null,
    devices: [],
    language: 'zh', // 默认中文
    i18n: {
      zh: {
        // 导航栏
        navigationTitle: '国仪核磁设备管理系统',
        // Tab栏
        tabHome: '首页',
        tabDevices: '设备',
        tabHelp: '帮助',
        // Home页面
        deviceName: '设备名称',
        liquidHelium: '液氦',
        liquidNitrogen: '液氮',
        temperature: '温度',
        pressure: '压力',
        empty: '空',
        full: '满',
        heliumStatusUpdate: '液氦状态更新日期',
        nitrogenStatusUpdate: '液氮状态更新日期',
        noRecords: '无记录',
        TemperatureMonitoring: '温度监控',
        liquidLevelMonitoring: '液位监控',
        detectionRecords: '检测记录',
        recentlyUpdated: '最近更新',
        noDeviceDetected: '未检测到绑定设备',
        noDeviceDescription: '您还没有绑定任何实验设备。请绑定设备以开始使用智能监控功能。',
        bindDeviceNow: '立即绑定设备',
        realtimeMonitoring: '实时监控',
        realtimeMonitoringDesc: '查看实验数据趋势',
        smartAlerts: '智能告警',
        smartAlertsDesc: '异常情况及时通知',
        dataStorage: '数据存储',
        dataStorageDesc: '自动保存实验记录',
        dataSharing: '数据共享',
        dataSharingDesc: '与团队成员协作',
        modifySuccess: '修改成功',
        modifyFailed: '修改失败',
        networkError: '网络错误',
        requestFailed: '请求失败，请重试',
        afterBinding: '绑定设备后，您可以',
        deviceBindingSteps: '设备绑定步骤',
        step1: '确保设备已通电并处于可连接状态',
        step2: '点击上方"立即绑定设备"按钮',
        step3: '从列表中选择您的设备或扫描设备二维码',
        step4: '按照提示完成绑定',
        // Devices页面
        deviceManagement: '设备管理',
        devicesCount: '个设备',
        viewDevice: '查看设备',
        unbindDevice: '解除绑定',
        bindNewDevice: '绑定新设备',
        scanFailed: '扫码失败，请重试',
        invalidDeviceId: '设备ID无效',
        deviceBindSuccess: '设备绑定成功',
        deviceUnbindSuccess: '设备解绑成功',
        unbindConfirmTitle: '解除绑定',
        unbindConfirmContent: '确定要解除绑定设备',
        // Help页面
        faq: '常见问题',
        afterSalesSupport: '售后支持',
        phoneSupport: '电话支持',
        emailSupport: '邮件支持',
        service24x7: '7×24小时服务',
        reply24h: '24小时内回复',
        callHotline: '拨打我们的客服热线获得即时帮助',
        sendEmail: '发送邮件至我们的支持团队',
        feedbackThanks: '感谢您的反馈！我们将尽快处理您的问题。',
        connectingService: '正在连接在线客服...',
        // FAQ内容
        faq1Question: '如何绑定新设备？',
        faq1Answer: '1. 确保设备已通电并处于配对模式（指示灯快速闪烁）\n2. 在设备管理页面点击底部"绑定新设备"按钮\n3. 选择要绑定的设备类型\n4. 按照屏幕提示完成绑定流程',
        faq1Tip: '如果遇到问题，请尝试重启设备后重试',
        faq2Question: '设备显示离线状态怎么办？',
        faq2Answer: '1. 检查设备电源是否正常\n2. 确认设备与路由器距离不超过10米\n3. 尝试重启路由器和设备\n4. 检查设备固件是否为最新版本',
        faq2Tip: '如果问题持续，请尝试重新绑定设备',
        faq3Question: '如何更新设备固件？',
        faq3Answer: '1. 进入设备详情页面\n2. 点击"设置"按钮\n3. 选择"固件更新"选项\n4. 如果有可用更新，点击"立即更新"',
        faq3Tip: '更新过程中请勿断电或关闭应用',
        faq4Question: '数据上传频率可以调整吗？',
        faq4Answer: '部分设备支持调整数据上传频率：\n1. 进入设备设置页面\n2. 选择"数据上传"选项\n3. 从预设频率中选择或自定义',
        faq4Tip: '更频繁的上传会消耗更多电量',
        faq5Question: '如何解除设备绑定？',
        faq5Answer: '1. 在设备卡片右上角点击解除绑定图标\n2. 确认解除绑定操作',
        faq5Tip: '解除绑定后，设备将不再显示在您的列表中\n如需重新绑定，请按照新设备流程操作'
      },
      en: {
        // Navigation
        navigationTitle: 'CIQTEK NMR Device Management System',
        // Tab bar
        tabHome: 'Home',
        tabDevices: 'Devices',
        tabHelp: 'Help',
        // Home page
        deviceName: 'Device Name',
        liquidHelium: 'Liquid Helium',
        liquidNitrogen: 'Liquid Nitrogen',
        temperature: 'Temperature',
        pressure: 'Pressure',
        empty: 'Empty',
        full: 'Full',
        heliumStatusUpdate: 'Liquid Helium Status Update Date',
        nitrogenStatusUpdate: 'Liquid Nitrogen Status Update Date',
        noRecords: 'No Records',
        TemperatureMonitoring: 'Temperature Monitoring',
        liquidLevelMonitoring: 'Liquid Level Monitoring',
        detectionRecords: 'Detection Records',
        recentlyUpdated: 'Recently Updated',
        noDeviceDetected: 'No Bound Device Detected',
        noDeviceDescription: 'You haven\'t bound any experimental equipment yet. Please bind a device to start using the intelligent monitoring features.',
        bindDeviceNow: 'Bind Device Now',
        realtimeMonitoring: 'Real-time Monitoring',
        realtimeMonitoringDesc: 'View experimental data trends',
        smartAlerts: 'Smart Alerts',
        smartAlertsDesc: 'Timely notifications for anomalies',
        dataStorage: 'Data Storage',
        dataStorageDesc: 'Automatic experiment record saving',
        dataSharing: 'Data Sharing',
        dataSharingDesc: 'Collaborate with team members',
        modifySuccess: 'Modification Successful',
        modifyFailed: 'Modification Failed',
        networkError: 'Network Error',
        requestFailed: 'Request failed, please try again',
        afterBinding: 'After binding a device, you can',
        deviceBindingSteps: 'Device Binding Steps',
        step1: 'Ensure the device is powered on and in connectable state',
        step2: 'Click the "Bind Device Now" button above',
        step3: 'Select your device from the list or scan the device QR code',
        step4: 'Follow the prompts to complete binding',
        // Devices page
        deviceManagement: 'Device Management',
        devicesCount: 'devices',
        viewDevice: 'View Device',
        unbindDevice: 'Unbind Device',
        bindNewDevice: 'Bind New Device',
        scanFailed: 'Scan failed, please try again',
        invalidDeviceId: 'Invalid device ID',
        deviceBindSuccess: 'Device bound successfully',
        deviceUnbindSuccess: 'Device unbound successfully',
        unbindConfirmTitle: 'Unbind Device',
        unbindConfirmContent: 'Are you sure you want to unbind device',
        // Help page
        faq: 'Frequently Asked Questions',
        afterSalesSupport: 'After-sales Support',
        phoneSupport: 'Phone Support',
        emailSupport: 'Email Support',
        service24x7: '7×24 Hours Service',
        reply24h: 'Reply within 24 hours',
        callHotline: 'Call our customer service hotline for instant help',
        sendEmail: 'Send email to our support team',
        feedbackThanks: 'Thank you for your feedback! We will handle your issue as soon as possible.',
        connectingService: 'Connecting to online customer service...',
        // FAQ content
        faq1Question: 'How to bind a new device?',
        faq1Answer: '1. Ensure the device is powered on and in pairing mode (indicator light flashing rapidly)\n2. Click the "Bind New Device" button at the bottom of the device management page\n3. Select the device type to bind\n4. Follow the on-screen prompts to complete the binding process',
        faq1Tip: 'If you encounter problems, please try restarting the device and try again',
        faq2Question: 'What to do when the device shows offline status?',
        faq2Answer: '1. Check if the device power is normal\n2. Confirm that the device is within 10 meters of the router\n3. Try restarting the router and device\n4. Check if the device firmware is the latest version',
        faq2Tip: 'If the problem persists, please try rebinding the device',
        faq3Question: 'How to update device firmware?',
        faq3Answer: '1. Enter the device details page\n2. Click the "Settings" button\n3. Select the "Firmware Update" option\n4. If there is an available update, click "Update Now"',
        faq3Tip: 'Do not power off or close the app during the update process',
        faq4Question: 'Can the data upload frequency be adjusted?',
        faq4Answer: 'Some devices support adjusting data upload frequency:\n1. Enter the device settings page\n2. Select the "Data Upload" option\n3. Choose from preset frequencies or customize',
        faq4Tip: 'More frequent uploads will consume more power',
        faq5Question: 'How to unbind a device?',
        faq5Answer: '1. Click the unbind icon in the upper right corner of the device card\n2. Confirm the unbind operation',
        faq5Tip: 'After unbinding, the device will no longer appear in your list\nTo rebind, please follow the new device process'
      }
    }
  },

  // 获取当前语言的文本
  t(key) {
    return this.globalData.i18n[this.globalData.language][key] || key;
  },

  // 切换语言
  switchLanguage(lang) {
    this.globalData.language = lang;
    wx.setStorageSync('language', lang);

    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: this.t('navigationTitle')
    });

    // 更新TabBar文本
    this.updateTabBar();

    // 通知所有页面更新语言
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.onLanguageChange) {
        page.onLanguageChange();
      }
    });
  },

  // 更新TabBar文本
  updateTabBar() {
    wx.setTabBarItem({
      index: 0,
      text: this.t('tabHome')
    });
    wx.setTabBarItem({
      index: 1,
      text: this.t('tabDevices')
    });
    wx.setTabBarItem({
      index: 2,
      text: this.t('tabHelp')
    });
  },
  onLaunch: function() {
    // 初始化语言设置
    const savedLanguage = wx.getStorageSync('language');
    if (savedLanguage) {
      this.globalData.language = savedLanguage;
    }

    // 检查本地存储中是否有登录态
    console.log('1. 检查本地存储中是否有登录态')
    const token = wx.getStorageSync('token');
    if (token) {
      this.globalData.token = token;
      // 可以在这里验证 token 是否过期
      this.checkSession();
    }
  },
  
  // 检查登录态是否过期
  checkSession: function() {
    console.log('2. 检查登录态是否过期')
    wx.checkSession({
      fail: () => {
        // 登录态过期，需要重新登录
        console.log('3. 登录态过期，需要重新登录')
        this.login();
      }
    });
  },


  // 获取设备列表
  fetchDeviceList: function () {
    wx.request({
      url: `https://qspinmini.ciqtek.com/api/user/devices`, // 替换为实际的 FastAPI 接口
      // url: `http://127.0.0.1:8060/api/user/devices`, // 替换为实际的 FastAPI 接口

      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
      },
      success: (res) => {
        console.log(res.data.data.devices)
        wx.setStorageSync('devices', res.data.data.devices)
      },
      fail: (err) => {
        wx.showToast({
          title: '请求失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  
  // 登录方法
  login: function() {
    console.log('4. 开始登录')
    return new Promise((resolve, reject) => {
      wx.login({
        success: res => {
          if (res.code) {
            // 获取到code后，发送到后端换取 token
            console.log('5. 获取到code后，发送到后端换取 token')
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/login',
              // url: 'http://127.0.0.1:8060/api/login',
              method: 'POST',
              data: {
                code: res.code
              },
              success: result => {
                if (result.data.code === 0) {
                  // 登录成功，保存 token
                  console.log('6. 登录成功，保存 token')
                  const token = result.data.data.token;
                  const userID = result.data.data.user_id;
                  
                  console.log(token)
                  wx.setStorageSync('token', token);
                  wx.setStorageSync('userID', userID);

                  this.globalData.token = token;
                  this.globalData.userID = userID;
                  this.fetchDeviceList();
                  resolve(result.data);
                } else {
                  console.log('7. 登录失败')
                  wx.showToast({
                    title: result.data.msg || '登录失败',
                    icon: 'none'
                  });
                  reject(result.data);
                }
              },
              fail: err => {
                console.log('8. 网络错误')
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
                reject(err);
              }
            });
          } else {
            console.log('9. 获取code失败')
            wx.showToast({
              title: '获取code失败',
              icon: 'none'
            });
            reject(new Error('获取code失败'));
          }
        },
        fail: err => {
          console.log('10. 微信登录失败')
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          });
          reject(err);
        }
      })
    });
  },
  
  // 获取用户信息
  getUserProfile: function() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          console.log(this.globalData.userInfo,this.globalData.token)
          
          // 将用户信息发送到后端
          if (this.globalData.token) {
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/user/update_info',
              method: 'POST',
              header: {
                'Authorization': `Bearer ${this.globalData.token}`
              },
              data: {
                userInfo: res.userInfo
              },
              success: result => {
                resolve(res.userInfo);
              },
              fail: err => {
                reject(err);
              }
            });
          } else {
            resolve(res.userInfo);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
})