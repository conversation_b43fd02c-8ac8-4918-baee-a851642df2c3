App({
  globalData: {
    userInfo: null,
    token: null,
    userID: null,
    devices: []
  },
  onLaunch: function() {
    // 检查本地存储中是否有登录态
    console.log('1. 检查本地存储中是否有登录态')
    const token = wx.getStorageSync('token');
    if (token) {
      this.globalData.token = token;
      // 可以在这里验证 token 是否过期
      this.checkSession();
    }
  },
  
  // 检查登录态是否过期
  checkSession: function() {
    console.log('2. 检查登录态是否过期')
    wx.checkSession({
      fail: () => {
        // 登录态过期，需要重新登录
        console.log('3. 登录态过期，需要重新登录')
        this.login();
      }
    });
  },


  // 获取设备列表
  fetchDeviceList: function () {
    wx.request({
      url: `https://qspinmini.ciqtek.com/api/user/devices`, // 替换为实际的 FastAPI 接口
      // url: `http://127.0.0.1:8060/api/user/devices`, // 替换为实际的 FastAPI 接口

      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
      },
      success: (res) => {
        console.log(res.data.data.devices)
        wx.setStorageSync('devices', res.data.data.devices)
      },
      fail: (err) => {
        wx.showToast({
          title: '请求失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  
  // 登录方法
  login: function() {
    console.log('4. 开始登录')
    return new Promise((resolve, reject) => {
      wx.login({
        success: res => {
          if (res.code) {
            // 获取到code后，发送到后端换取 token
            console.log('5. 获取到code后，发送到后端换取 token')
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/login',
              // url: 'http://127.0.0.1:8060/api/login',
              method: 'POST',
              data: {
                code: res.code
              },
              success: result => {
                if (result.data.code === 0) {
                  // 登录成功，保存 token
                  console.log('6. 登录成功，保存 token')
                  const token = result.data.data.token;
                  const userID = result.data.data.user_id;
                  
                  console.log(token)
                  wx.setStorageSync('token', token);
                  wx.setStorageSync('userID', userID);

                  this.globalData.token = token;
                  this.globalData.userID = userID;
                  this.fetchDeviceList();
                  resolve(result.data);
                } else {
                  console.log('7. 登录失败')
                  wx.showToast({
                    title: result.data.msg || '登录失败',
                    icon: 'none'
                  });
                  reject(result.data);
                }
              },
              fail: err => {
                console.log('8. 网络错误')
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
                reject(err);
              }
            });
          } else {
            console.log('9. 获取code失败')
            wx.showToast({
              title: '获取code失败',
              icon: 'none'
            });
            reject(new Error('获取code失败'));
          }
        },
        fail: err => {
          console.log('10. 微信登录失败')
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          });
          reject(err);
        }
      })
    });
  },
  
  // 获取用户信息
  getUserProfile: function() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          console.log(this.globalData.userInfo,this.globalData.token)
          
          // 将用户信息发送到后端
          if (this.globalData.token) {
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/user/update_info',
              method: 'POST',
              header: {
                'Authorization': `Bearer ${this.globalData.token}`
              },
              data: {
                userInfo: res.userInfo
              },
              success: result => {
                resolve(res.userInfo);
              },
              fail: err => {
                reject(err);
              }
            });
          } else {
            resolve(res.userInfo);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
})