App({
  globalData: {
    userInfo: null,
    token: null,
    userID: null,
    devices: [],
    language: 'zh', // 默认中文
    i18n: {
      zh: {
        // 导航栏
        navigationTitle: '国仪核磁设备管理系统',
        // Tab栏
        tabHome: '首页',
        tabDevices: '设备',
        tabHelp: '帮助',
        // Home页面
        deviceName: '设备名称',
        liquidHelium: '液氦',
        liquidNitrogen: '液氮',
        temperature: '温度',
        pressure: '压力',
        empty: '空',
        full: '满',
        heliumStatusUpdate: '液氦状态更新日期',
        nitrogenStatusUpdate: '液氮状态更新日期',
        noRecords: '无记录',
        liquidLevelMonitoring: '液位监控',
        detectionRecords: '检测记录',
        recentlyUpdated: '最近更新',
        noDeviceDetected: '未检测到绑定设备',
        noDeviceDescription: '您还没有绑定任何实验设备。请绑定设备以开始使用智能监控功能。',
        bindDeviceNow: '立即绑定设备',
        realtimeMonitoring: '实时监控',
        realtimeMonitoringDesc: '查看实验数据趋势',
        smartAlerts: '智能告警',
        smartAlertsDesc: '异常情况及时通知',
        dataStorage: '数据存储',
        dataStorageDesc: '自动保存实验记录',
        dataSharing: '数据共享',
        dataSharingDesc: '与团队成员协作',
        modifySuccess: '修改成功',
        modifyFailed: '修改失败',
        networkError: '网络错误',
        requestFailed: '请求失败，请重试',
        afterBinding: '绑定设备后，您可以',
        deviceBindingSteps: '设备绑定步骤',
        step1: '确保设备已通电并处于可连接状态',
        step2: '点击上方"立即绑定设备"按钮',
        step3: '从列表中选择您的设备或扫描设备二维码',
        step4: '按照提示完成绑定'
      },
      en: {
        // Navigation
        navigationTitle: 'CIQTEK NMR Device Management System',
        // Tab bar
        tabHome: 'Home',
        tabDevices: 'Devices',
        tabHelp: 'Help',
        // Home page
        deviceName: 'Device Name',
        liquidHelium: 'Liquid Helium',
        liquidNitrogen: 'Liquid Nitrogen',
        temperature: 'Temperature',
        pressure: 'Pressure',
        empty: 'Empty',
        full: 'Full',
        heliumStatusUpdate: 'Liquid Helium Status Update Date',
        nitrogenStatusUpdate: 'Liquid Nitrogen Status Update Date',
        noRecords: 'No Records',
        liquidLevelMonitoring: 'Liquid Level Monitoring',
        detectionRecords: 'Detection Records',
        recentlyUpdated: 'Recently Updated',
        noDeviceDetected: 'No Bound Device Detected',
        noDeviceDescription: 'You haven\'t bound any experimental equipment yet. Please bind a device to start using the intelligent monitoring features.',
        bindDeviceNow: 'Bind Device Now',
        realtimeMonitoring: 'Real-time Monitoring',
        realtimeMonitoringDesc: 'View experimental data trends',
        smartAlerts: 'Smart Alerts',
        smartAlertsDesc: 'Timely notifications for anomalies',
        dataStorage: 'Data Storage',
        dataStorageDesc: 'Automatic experiment record saving',
        dataSharing: 'Data Sharing',
        dataSharingDesc: 'Collaborate with team members',
        modifySuccess: 'Modification Successful',
        modifyFailed: 'Modification Failed',
        networkError: 'Network Error',
        requestFailed: 'Request failed, please try again',
        afterBinding: 'After binding a device, you can',
        deviceBindingSteps: 'Device Binding Steps',
        step1: 'Ensure the device is powered on and in connectable state',
        step2: 'Click the "Bind Device Now" button above',
        step3: 'Select your device from the list or scan the device QR code',
        step4: 'Follow the prompts to complete binding'
      }
    }
  },

  // 获取当前语言的文本
  t(key) {
    return this.globalData.i18n[this.globalData.language][key] || key;
  },

  // 切换语言
  switchLanguage(lang) {
    this.globalData.language = lang;
    wx.setStorageSync('language', lang);

    // 更新导航栏标题
    wx.setNavigationBarTitle({
      title: this.t('navigationTitle')
    });

    // 更新TabBar文本
    this.updateTabBar();

    // 通知所有页面更新语言
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.onLanguageChange) {
        page.onLanguageChange();
      }
    });
  },

  // 更新TabBar文本
  updateTabBar() {
    wx.setTabBarItem({
      index: 0,
      text: this.t('tabHome')
    });
    wx.setTabBarItem({
      index: 1,
      text: this.t('tabDevices')
    });
    wx.setTabBarItem({
      index: 2,
      text: this.t('tabHelp')
    });
  },
  onLaunch: function() {
    // 初始化语言设置
    const savedLanguage = wx.getStorageSync('language');
    if (savedLanguage) {
      this.globalData.language = savedLanguage;
    }

    // 检查本地存储中是否有登录态
    console.log('1. 检查本地存储中是否有登录态')
    const token = wx.getStorageSync('token');
    if (token) {
      this.globalData.token = token;
      // 可以在这里验证 token 是否过期
      this.checkSession();
    }
  },
  
  // 检查登录态是否过期
  checkSession: function() {
    console.log('2. 检查登录态是否过期')
    wx.checkSession({
      fail: () => {
        // 登录态过期，需要重新登录
        console.log('3. 登录态过期，需要重新登录')
        this.login();
      }
    });
  },


  // 获取设备列表
  fetchDeviceList: function () {
    wx.request({
      url: `https://qspinmini.ciqtek.com/api/user/devices`, // 替换为实际的 FastAPI 接口
      // url: `http://127.0.0.1:8060/api/user/devices`, // 替换为实际的 FastAPI 接口

      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
      },
      success: (res) => {
        console.log(res.data.data.devices)
        wx.setStorageSync('devices', res.data.data.devices)
      },
      fail: (err) => {
        wx.showToast({
          title: '请求失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  
  // 登录方法
  login: function() {
    console.log('4. 开始登录')
    return new Promise((resolve, reject) => {
      wx.login({
        success: res => {
          if (res.code) {
            // 获取到code后，发送到后端换取 token
            console.log('5. 获取到code后，发送到后端换取 token')
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/login',
              // url: 'http://127.0.0.1:8060/api/login',
              method: 'POST',
              data: {
                code: res.code
              },
              success: result => {
                if (result.data.code === 0) {
                  // 登录成功，保存 token
                  console.log('6. 登录成功，保存 token')
                  const token = result.data.data.token;
                  const userID = result.data.data.user_id;
                  
                  console.log(token)
                  wx.setStorageSync('token', token);
                  wx.setStorageSync('userID', userID);

                  this.globalData.token = token;
                  this.globalData.userID = userID;
                  this.fetchDeviceList();
                  resolve(result.data);
                } else {
                  console.log('7. 登录失败')
                  wx.showToast({
                    title: result.data.msg || '登录失败',
                    icon: 'none'
                  });
                  reject(result.data);
                }
              },
              fail: err => {
                console.log('8. 网络错误')
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
                reject(err);
              }
            });
          } else {
            console.log('9. 获取code失败')
            wx.showToast({
              title: '获取code失败',
              icon: 'none'
            });
            reject(new Error('获取code失败'));
          }
        },
        fail: err => {
          console.log('10. 微信登录失败')
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          });
          reject(err);
        }
      })
    });
  },
  
  // 获取用户信息
  getUserProfile: function() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo;
          console.log(this.globalData.userInfo,this.globalData.token)
          
          // 将用户信息发送到后端
          if (this.globalData.token) {
            wx.request({
              url: 'https://qspinmini.ciqtek.com/api/user/update_info',
              method: 'POST',
              header: {
                'Authorization': `Bearer ${this.globalData.token}`
              },
              data: {
                userInfo: res.userInfo
              },
              success: result => {
                resolve(res.userInfo);
              },
              fail: err => {
                reject(err);
              }
            });
          } else {
            resolve(res.userInfo);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
})