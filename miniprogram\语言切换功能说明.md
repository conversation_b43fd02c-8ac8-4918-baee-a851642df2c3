# 全局语言切换功能实现说明

## 功能概述
在home页面添加了全局语言切换按钮，支持中文和英文之间的切换，默认语言为中文。

## 实现的功能

### 1. 全局语言管理
- 在 `app.js` 中添加了全局语言状态管理
- 支持中文(zh)和英文(en)两种语言
- 语言设置会保存到本地存储，下次启动时会记住用户的选择

### 2. 语言切换按钮
- 在home页面右上角添加了语言切换按钮
- 按钮显示当前可切换到的语言（中文时显示"EN"，英文时显示"中"）
- 点击按钮可以在中英文之间切换

### 3. 多语言文本支持
已翻译的文本包括：
- 导航栏标题
- Tab栏文本（首页、设备、帮助）
- 设备状态相关文本
- 液氦、液氮监控文本
- 设备绑定相关文本
- 功能特点介绍文本
- 错误提示信息

### 4. 动态更新
- 切换语言后，页面文本会立即更新
- TabBar文本也会同步更新
- 导航栏标题会同步更新

## 文件修改说明

### 1. app.js
- 添加了 `globalData.language` 和 `globalData.i18n` 
- 添加了 `t()` 方法用于获取翻译文本
- 添加了 `switchLanguage()` 方法用于切换语言
- 添加了 `updateTabBar()` 方法用于更新TabBar文本
- 在 `onLaunch` 中添加了语言初始化逻辑

### 2. pages/home/<USER>
- 添加了 `currentLanguage` 和 `texts` 数据字段
- 添加了 `initLanguage()` 方法初始化语言
- 添加了 `getTexts()` 方法获取当前语言文本
- 添加了 `onLanguageChange()` 回调方法
- 添加了 `switchLanguage()` 方法处理语言切换
- 更新了错误提示信息使用多语言文本

### 3. pages/home/<USER>
- 在页面顶部添加了语言切换按钮
- 将所有硬编码的中英文文本替换为动态文本绑定
- 支持有设备和无设备两种状态下的多语言显示

### 4. pages/home/<USER>
- 添加了语言切换按钮的样式
- 按钮采用现代化设计，支持悬停效果

## 使用方法
1. 打开小程序，进入home页面
2. 点击右上角的语言切换按钮（显示"EN"或"中"）
3. 页面文本会立即切换到对应语言
4. 语言设置会自动保存，下次打开应用时会保持上次的选择

## 扩展说明
- 如需添加更多语言，可以在 `app.js` 的 `i18n` 对象中添加新的语言包
- 如需为其他页面添加多语言支持，可以参考home页面的实现方式
- 所有文本都集中在 `app.js` 中管理，便于维护和更新
