.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: aquamarine;
  /* background-image: url('你的背景图片地址');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; */
}

/* 调整容器样式以适应背景 */
.container {
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.9); /* 半透明白色背景 */
  min-height: calc(100vh - 100rpx); /* 减去底部导航栏高度 */
  padding-bottom: 100rpx; /* 为底部导航留出空间 */
}
.action-btnx{
	text-align: center;
	display: flex;
	justify-content: center;
	height: 80rpx;
}

/* 调整底部导航样式 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95); /* 半透明导航栏 */
} 