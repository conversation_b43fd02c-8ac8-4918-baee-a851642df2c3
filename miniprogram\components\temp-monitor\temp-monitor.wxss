.temp-gauge {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.temp-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.temp-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.temp-icon.warning {
  background: rgba(241, 196, 15, 0.1);
  color: #f39c12;
}

.temp-icon.danger {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.temp-details {
  flex: 1;
}

.temp-label {
  font-size: 32rpx;
  color: #34495e;
  font-weight: 500;
}

.temp-value-section {
  display: flex;
  align-items: baseline;
}

.temp-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-right: 8rpx;
}

.temp-unit {
  font-size: 24rpx;
  color: #95a5a6;
}

.temp-bar {
  width: 100%;
  height: 16rpx;
  background: #f5f7fa;
  border-radius: 8rpx;
  overflow: hidden;
  margin: 12rpx 0;
}

.temp-bar-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
  background: linear-gradient(90deg, #3498db, #2980b9);
}

.temp-bar-fill.warning {
  background: linear-gradient(90deg, #f1c40f, #f39c12);
}

.temp-bar-fill.danger {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.temp-range {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #95a5a6;
} 