.container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}



.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #1F2937;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #EFF6FF;
  display: flex;
  align-items: center;
  justify-content: center;
}

.online{
  background-color: #10B981;
}
.offline{
  background-color: #EF4444;
}


.status-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.status-card:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.green {
  background-color: #10B981;
}

.status-dot.yellow {
  background-color: #F59E0B;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.status-text.green {
  color: #10B981;
}

.status-text.yellow {
  color: #D97706;
}

.grid {
  display: flex;
}

.grid-cols-2 {
  flex-direction: row;
}

.gap-4 {
  gap: 16px;
}

.info-card {
  border-radius: 8px;
  padding: 12px;
  width: 50%;
}

.info-card.blue {
  background-color: #EFF6FF;
}

.info-card.purple {
  background-color: #F5F3FF;
}

.info-card.red {
  background-color: #f1f1f1;
}

.info-card.orange {
  background-color: #FFEDD5;
}

.info-card.indigo {
  background-color: #EEF2FF;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: #4B5563;
  margin-left: 8px;
}

.info-value {
  font-size: 20px;
  font-weight: bold;
}

.info-card.blue .info-value {
  color: #1D4ED8;
}

.info-card.purple .info-value {
  color: #7C3AED;
}

.info-card.red .info-value {
  color: #636060;
}

.info-card.orange .info-value {
  color: #EA580C;
}

.info-card.indigo .info-value {
  color: #4F46E5;
}

.gauge {
  height: 8px;
  border-radius: 4px;
}

.gauge.bg-gray {
  background-color: #E5E7EB;
}

.gauge-fill {
  height: 100%;
}

.gauge-fill.blue {
  background: linear-gradient(to right, #cccccc, #333333);
}

.gauge-fill.orange {
  background: linear-gradient(to right, #FBBF24, #EF4444);
}

.text-gray {
  color: #6B7280;
}

.relative {
  position: relative;
}

.h-2 {
  height: 8px;
}

.bg-gray {
  background-color: #E5E7EB;
}

.rounded-full {
  border-radius: 9999px;
}

.overflow-hidden {
  overflow: hidden;
}

.absolute {
  position: absolute;
}

.top-0 {
  top: 0;
}

.left-0 {
  left: 0;
}

.h-full {
  height: 100%;
}

.bg-blue {
  background: linear-gradient(to right, #BFDBFE, #3B82F6);
}

.bg-indigo {
  background: linear-gradient(to right, #C7D2FE, #6366F1);
}

.text-yellow {
  color: #D97706;
}

.date {
  font-size: 18px;
  font-weight: bold;
  color: #1F2937;
}

.time {
  font-size: 14px;
  color: #6B7280;
}

.refresh-btn {
  width: 100%;
  margin-top: 16px;
  background-color: #3B82F6;
  color: white;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.refresh-btn:active {
  background-color: #2563EB;
}

.blink {
  animation: blink 1.5s infinite;
}

@keyframes blink {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.mb-3 {
  margin-bottom: 12px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.text-xs {
  font-size: 12px;
}

.text-right {
  text-align: right;
}

.bc-date{
  display: flex;
  flex-direction: column;
}

/* 添加禁用状态样式 */
.status-card.disabled {
  opacity: 0.6;
  pointer-events: none;
  background-color: #f5f5f5;
  position: relative;
}

.status-card.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

/* 禁用状态下的文字颜色 */
.status-card.disabled .section-title,
.status-card.disabled .info-label,
.status-card.disabled .info-value,
.status-card.disabled .time {
  color: #9CA3AF;
}

/* 禁用状态下的图标颜色 */
.status-card.disabled .status-dot {
  opacity: 0.5;
}

/* 更新记录容器样式 */
.update-time-container {
  background-color: #F8FAFC;
  border-radius: 8px;
  padding: 16px;
}

/* 更新记录项样式 */
.update-time-item {
  padding: 8px 0;
}

/* 更新记录头部样式 */
.update-time-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

/* 更新记录标签样式 */
.update-time-label {
  font-size: 14px;
  font-weight: 500;
  color: #4B5563;
  margin-left: 8px;
}

/* 更新记录值样式 */
.update-time-value {
  font-size: 16px;
  font-weight: 600;
  color: #1F2937;
  margin-left: 24px;
  display: block;
}

/* 分隔线样式 */
.update-time-divider {
  height: 1px;
  background-color: #E5E7EB;
  margin: 4px 0;
}

/* 蓝色文字样式 */
.status-text.blue {
  color: #3B82F6;
  margin-left: 4px;
}

/* 禁用状态下的更新记录样式 */
.status-card.disabled .update-time-container {
  background-color: #F3F4F6;
}

.status-card.disabled .update-time-label {
  color: #9CA3AF;
}

.status-card.disabled .update-time-value {
  color: #9CA3AF;
}

.status-card.disabled .update-time-divider {
  background-color: #D1D5DB;
}

/* 简化版标题样式 */
.section-title-container {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 12px;
}

.section-title-icon {
  margin-right: 8px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1F2937;
  position: relative;
}

.section-title-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 18px;
  background-color: #3B82F6;
  border-radius: 2px;
}

/* pages/device-binding/device-binding.wxss */

/* 引入图标字体 */
@font-face {
  font-family: "iconfont";
  src: url('https://at.alicdn.com/t/font_2553510_ukfzwfn4pb.ttf');
}

.iconfont {
  font-family: "iconfont";
}

/* 图标定义 */
.icon-microscope:before { content: "\f2cc"; }
.icon-exclamation:before { content: "\f12a"; }
.icon-link:before { content: "\f0c1"; margin-right: 8rpx; }
.icon-chart-line:before { content: "\f201"; }
.icon-bell:before { content: "\f0f3"; }
.icon-database:before { content: "\f1c0"; }
.icon-share-alt:before { content: "\f1e0"; }
.icon-info-circle:before { content: "\f05a"; margin-right: 8rpx; }

/* 整体容器 */
.device-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}

/* 设备图标 */
.device-icon {
  margin-bottom: 40rpx;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-30rpx);
  }
}

.device-icon-inner {
  position: relative;
  display: inline-block;
}

.icon-bg {
  width: 160rpx;
  height: 160rpx;
  background-color: #e6f0ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-bg .iconfont {
  font-size: 80rpx;
  color: #3b82f6;
}

.warning-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #f56565;
  color: white;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.warning-badge .iconfont {
  font-size: 24rpx;
}

/* 标题和描述 */
.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 16rpx;
  text-align: center;
}

.description {
  font-size: 28rpx;
  color: #718096;
  margin-bottom: 32rpx;
  text-align: center;
}

/* 绑定按钮 */
.bind-btn {
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  padding: 20rpx 40rpx;
  border-radius: 999rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 15rpx -3rpx rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.bind-btn::after {
  border: none;
}

/* 分割线 */
.divider {
  position: relative;
  width: 100%;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider-line {
  position: absolute;
  height: 1px;
  width: 100%;
  background-color: #e2e8f0;
}

.divider-text {
  position: relative;
  background-color: #f8f9fa;
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #718096;
}

/* 功能特点 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  width: 100%;
  margin-bottom: 40rpx;
}

.feature-card {
  background-color: white;
  padding: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 1rpx 3rpx 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.3s ease;
  overflow: hidden;
}

.feature-card:active {
  transform: translateY(-5rpx);
}

.feature-icon-bg {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.feature-icon-bg.blue {
  background-color: #ebf8ff;
}

.feature-icon-bg.green {
  background-color: #f0fff4;
}

.feature-icon-bg.purple {
  background-color: #faf5ff;
}

.feature-icon-bg.yellow {
  background-color: #fffbeb;
}

.feature-icon-bg .iconfont {
  font-size: 30rpx;
}

.blue .iconfont {
  color: #3b82f6;
}

.green .iconfont {
  color: #10b981;
}

.purple .iconfont {
  color: #8b5cf6;
}

.yellow .iconfont {
  color: #f59e0b;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 6rpx;
}

.feature-desc {
  font-size: 22rpx;
  color: #718096;
}

/* 绑定步骤 */
.bind-steps {
  width: 100%;
  background-color: #ebf8ff;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.steps-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #2b6cb0;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
}

.step-number {
  background-color: #3b82f6;
  color: white;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  margin-right: 10rpx;
  margin-top: 4rpx;
}

.step-text {
  font-size: 26rpx;
  color: #4a5568;
  flex: 1;
}

/* 帮助链接 */
.help-links {
  font-size: 24rpx;
  color: #718096;
  text-align: center;
}

.link {
  color: #3b82f6;
  font-weight: 500;
  margin: 0 8rpx;
}

.mr-2 {
  margin-right: 0.5rem;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.back-button image {
  margin-right: 10rpx;
}

.back-button text {
  color: #333;
  font-size: 28rpx;
}

.edit-btn {
  margin-left: 8px;
  padding: 4px;
  border-radius: 4px;
  background-color: #EFF6FF;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-btn:active {
  background-color: #DBEAFE;
  transform: scale(0.95);
}

