.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 8s infinite;
}

.bubble:nth-child(1) {
  width: 100px;
  height: 100px;
  left: 10%;
  top: 20%;
  animation-delay: 0s;
}

.bubble:nth-child(2) {
  width: 150px;
  height: 150px;
  right: 15%;
  top: 30%;
  animation-delay: 2s;
}

.bubble:nth-child(3) {
  width: 80px;
  height: 80px;
  left: 20%;
  bottom: 30%;
  animation-delay: 4s;
}

.bubble:nth-child(4) {
  width: 120px;
  height: 120px;
  right: 25%;
  bottom: 20%;
  animation-delay: 6s;
}

.bubble:nth-child(5) {
  width: 60px;
  height: 60px;
  left: 50%;
  top: 50%;
  animation-delay: 8s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.content {
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.logo {
  margin-bottom: 20px;
}

.logo-img {
  width: 120px;
  height: 120px;
}

.title {
  font-size: 24px;
  color: #333;
  margin-bottom: 40px;
  font-weight: bold;
}

.login-btn{
  width: 80%;
  height: 44px;
  margin-bottom: 20px;
  border-radius: 22px;
  background: #07c160;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.home-btn {
  width: 80%;
  height: 44px;
  margin-bottom: 20px;
  border-radius: 22px;
  background: #07c160;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-content text {
  font-size: 16px;
}
