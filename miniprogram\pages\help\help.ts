Page({
  data: {
    faqs: [
      {
        question: '如何绑定新设备？',
        answer: '1. 确保设备已通电并处于配对模式（指示灯快速闪烁）\n2. 在设备管理页面点击底部"绑定新设备"按钮\n3. 选择要绑定的设备类型\n4. 按照屏幕提示完成绑定流程',
        tip: '如果遇到问题，请尝试重启设备后重试',
        isActive: false
      },
      {
        question: '设备显示离线状态怎么办？',
        answer: '1. 检查设备电源是否正常\n2. 确认设备与路由器距离不超过10米\n3. 尝试重启路由器和设备\n4. 检查设备固件是否为最新版本',
        tip: '如果问题持续，请尝试重新绑定设备',
        isActive: false
      },
      {
        question: '如何更新设备固件？',
        answer: '1. 进入设备详情页面\n2. 点击"设置"按钮\n3. 选择"固件更新"选项\n4. 如果有可用更新，点击"立即更新"',
        tip: '更新过程中请勿断电或关闭应用',
        isActive: false
      },
      {
        question: '数据上传频率可以调整吗？',
        answer: '部分设备支持调整数据上传频率：\n1. 进入设备设置页面\n2. 选择"数据上传"选项\n3. 从预设频率中选择或自定义',
        tip: '更频繁的上传会消耗更多电量',
        isActive: false
      },
      {
        question: '如何解除设备绑定？',
        answer: '1. 在设备卡片右上角点击解除绑定图标\n2. 确认解除绑定操作',
        tip: '解除绑定后，设备将不再显示在您的列表中\n如需重新绑定，请按照新设备流程操作',
        isActive: false
      }
    ]
  },

  navigateBack() {
    wx.navigateBack();
  },

  toggleFaq(e: any) {
    const index = e.currentTarget.dataset.index;
    const newFaqs = [...this.data.faqs];
    newFaqs[index].isActive = !newFaqs[index].isActive;

    this.setData({
      faqs: newFaqs
    });
  },

  submitFeedback() {
    wx.showToast({
      title: '感谢您的反馈！我们将尽快处理您的问题。',
      icon: 'none'
    });
  },

  startChat() {
    wx.showToast({
      title: '正在连接在线客服...',
      icon: 'none'
    });
  }
});