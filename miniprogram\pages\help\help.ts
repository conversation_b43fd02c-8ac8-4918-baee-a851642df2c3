Page({
  data: {
    currentLanguage: 'zh',
    texts: {},
    faqs: []
  },

  onLoad() {
    this.initLanguage();
  },

  // 初始化语言
  initLanguage() {
    const app = getApp();
    this.setData({
      currentLanguage: app.globalData.language,
      texts: this.getTexts(app.globalData.language)
    });
    this.updateFaqs();
  },

  // 获取当前语言的文本
  getTexts(lang) {
    const app = getApp();
    return app.globalData.i18n[lang];
  },

  // 语言变化回调
  onLanguageChange() {
    // this.initLanguage();
    wx.reLaunch({
      url: '/pages/home/<USER>' // 替换为你项目的首页路径
    });
  },

  // 切换语言
  switchLanguage() {
    const app = getApp();
    const newLang = app.globalData.language === 'zh' ? 'en' : 'zh';
    app.switchLanguage(newLang);
    this.initLanguage();
  },

  // 更新FAQ数据
  updateFaqs() {
    const texts = this.data.texts;
    this.setData({
      faqs: [
        {
          question: texts.faq1Question,
          answer: texts.faq1Answer,
          tip: texts.faq1Tip,
          isActive: false
        },
        {
          question: texts.faq2Question,
          answer: texts.faq2Answer,
          tip: texts.faq2Tip,
          isActive: false
        },
        {
          question: texts.faq3Question,
          answer: texts.faq3Answer,
          tip: texts.faq3Tip,
          isActive: false
        },
        {
          question: texts.faq4Question,
          answer: texts.faq4Answer,
          tip: texts.faq4Tip,
          isActive: false
        },
        {
          question: texts.faq5Question,
          answer: texts.faq5Answer,
          tip: texts.faq5Tip,
          isActive: false
        }
      ]
    });
  },

  navigateBack() {
    wx.navigateBack();
  },

  toggleFaq(e: any) {
    const index = e.currentTarget.dataset.index;
    const newFaqs = [...this.data.faqs];
    newFaqs[index].isActive = !newFaqs[index].isActive;

    this.setData({
      faqs: newFaqs
    });
  },

  submitFeedback() {
    wx.showToast({
      title: this.data.texts.feedbackThanks,
      icon: 'none'
    });
  },

  startChat() {
    wx.showToast({
      title: this.data.texts.connectingService,
      icon: 'none'
    });
  }
});