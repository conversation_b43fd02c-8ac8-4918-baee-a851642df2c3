<view class="container">
  <!-- Header -->
  <view class="header">
    <text class="title">{{texts.deviceManagement}}</text>
    <view class="device-count">
      <text class="count-text">{{devices.length}}</text>
      <text> {{texts.devicesCount}}</text>
    </view>
  </view>
  
  <!-- Device List -->
  <view class="device-list">
    <block wx:for="{{devices}}" wx:key="device_id">
      <view class="device-card">
        <view class="card-header">
          <view class="device-info">
            <text class="device-name">{{!!item.device_name ? item.device_name : item.device_id}}</text>
            <!-- <view class="device-status">
              <view class="status-indicator {{item.status.status}}"></view>
              <text class="status-text">{{item.status.status}}</text>
            </view> -->
          </view>
        </view>
        
        <view class="device-actions">
          <button class="view-btn" bindtap="viewDevice" data-id="{{item.device_id}}">
            <icon type="eye" size="16" color="#3B82F6"></icon>
            <text>{{texts.viewDevice}}</text>
          </button>
          <button class="settings-btn" bindtap="unbindDevice" data-id="{{item.device_id}}">
            <icon type="setting" size="16" color="#6B7280"></icon>
            <text>{{texts.unbindDevice}}</text>
          </button>
        </view>
      </view>
    </block>
  </view>
  
  <!-- Bind Device Button -->
  <button class="bind-btn" bindtap="onScan">
    <icon type="add" size="20" color="#FFFFFF"></icon>
    <text>{{texts.bindNewDevice}}</text>
  </button>
</view>