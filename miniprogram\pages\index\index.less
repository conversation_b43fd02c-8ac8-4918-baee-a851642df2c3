/**index.less**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.container {
  width: 100%;
  margin: 0 auto;
  padding: 40rpx 30rpx;
  background-color: #f5f7fa;
  color: #333;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system-font, "PingFang SC", "Helvetica Neue", sans-serif;
}

.content-section{
  padding-bottom: 120rpx;
}
.device-info-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.device-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.device-name-section {
  display: flex;
  align-items: center;
  
  .iconfont {
    font-size: 40rpx;
    color: #3498db;
    margin-right: 16rpx;
  }
}

.device-title {
  font-size: 32rpx;
  color: #2c3e50;
}

.device-id {
  font-size: 24rpx;
  color: #95a5a6;
  background-color: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.device-meta {
  display: flex;
  gap: 30rpx;
  
  .meta-item {
    display: flex;
    align-items: center;
    
    .label {
      font-size: 32rpx;
      color: #7f8c8d;
      margin-right: 8rpx;
    }
    
    .value {
      font-size: 32rpx;
      color: #2c3e50;
      font-weight: 500;
    }
  }
}

.header {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.user-info {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ddd;
  margin-right: 16rpx;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  
  // .iconfont {
  //   margin-right: 16rpx;
  //   color: #3498db;
  // }
}

.gauges-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
}

.gauge {
  width: 48%;
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  display: flex;
  flex-direction: column;
}

.gauge-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  .icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12rpx;
    
    &.helium {
      background: rgba(52, 152, 219, 0.1);
      color: #3498db;
    }
    
    &.nitrogen {
      background: rgba(155, 89, 182, 0.1);
      color: #9b59b6;
    }
  }
}

.gauge-title {
  font-size: 28rpx;
  color: #34495e;
  font-weight: 500;
}

.gauge-value-section {
  margin-bottom: 16rpx;
}

.gauge-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-right: 8rpx;
}

.gauge-unit {
  font-size: 24rpx;
  color: #95a5a6;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background: #f5f7fa;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 12rpx;
  
  .progress-fill {
    height: 100%;
    border-radius: 8rpx;
    transition: width 0.3s ease;
    
    &.helium {
      background: linear-gradient(90deg, #3498db, #2980b9);
    }
    
    &.nitrogen {
      background: linear-gradient(90deg, #9b59b6, #8e44ad);
    }
    
    &.warning {
      background: linear-gradient(90deg, #f1c40f, #f39c12);
    }
    
    &.danger {
      background: linear-gradient(90deg, #e74c3c, #c0392b);
    }
  }
}

.gauge-meta {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #95a5a6;
  
  .min-max {
    display: flex;
    gap: 16rpx;
  }
  
  .status {
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    
    &.normal {
      background: rgba(46, 204, 113, 0.1);
      color: #27ae60;
    }
    
    &.warning {
      background: rgba(241, 196, 15, 0.1);
      color: #f39c12;
    }
    
    &.danger {
      background: rgba(231, 76, 60, 0.1);
      color: #e74c3c;
    }
  }
}

.temp-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.temp-gauge {
  width: 48%;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
}

.temp-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.temp-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
  color: #3498db;
}

.temp-details {
  flex-grow: 1;
}

.temp-label {
  font-size: 28rpx;
  color: #666;
}

.temp-value {
  font-size: 48rpx;
  font-weight: bold;
}

.temp-unit {
  font-size: 24rpx;
  color: #666;
}

.temp-bar {
  height: 12rpx;
  background-color: #e9ecef;
  border-radius: 6rpx;
  overflow: hidden;
  margin-top: 10rpx;
}

.temp-bar-fill {
  height: 100%;
  border-radius: 6rpx;
  
  &.normal {
    background-color: #2ecc71;
  }
  
  &.warning {
    background-color: #f39c12;
  }
  
  &.danger {
    background-color: #e74c3c;
  }
}

.temp-range {
  display: flex;
  justify-content: space-between;
  font-size: 20rpx;
  color: #adb5bd;
  margin-top: 10rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  align-items: center;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.status-label {
  color: #666;
  font-size: 28rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: white;
  
  &.green {
    background-color: #2ecc71;
  }
  
  &.red {
    background-color: #e74c3c;
  }
}

.nav-tabs {
  display: flex;
  background-color: white;
  border-radius: 24rpx;
  padding: 10rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.nav-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  border-radius: 16rpx;
  color: #666;
  
  &.active {
    background-color: #3498db;
    color: white;
  }
}

.device-list {
  list-style: none;
}

.device-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

.device-name {
  font-weight: bold;
  margin-bottom: 10rpx;
}

.device-status {
  color: #666;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.device-actions {
  display: flex;
  align-items: center;
  width: 200rpx;
}

.action-btn {
  border: none;
  background-color: #3498db;
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  line-height: 1;
  min-height: unset;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  display: flex;
  border-top: 2rpx solid #eee;
}

.bottom-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0 66rpx 0;
  font-size: 26rpx;
  color: #666;
  
  &.active {
    color: #3498db;
  }
  
  .iconfont {
    font-size: 40rpx;
    margin-bottom: 10rpx;
  }
}

.help-item {
  padding: 30rpx 0;
  border-bottom: 2rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

.help-question {
  font-weight: bold;
  margin-bottom: 20rpx;
}

.help-answer {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 提供微信小程序的图标样式 */
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_YOUR_FONT_ID.woff2') format('woff2'),
       url('//at.alicdn.com/t/font_YOUR_FONT_ID.woff') format('woff'),
       url('//at.alicdn.com/t/font_YOUR_FONT_ID.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
}

/* 用伪类模拟图标 */
.icon-status:before { content: "\e600"; }
.icon-helium:before { content: "\e601"; }
.icon-nitrogen:before { content: "\e602"; }
.icon-experiment:before { content: "\e603"; }
.icon-devices:before { content: "\e604"; }
.icon-home:before { content: "\e605"; }
.icon-list:before { content: "\e606"; }
.icon-help:before { content: "\e607"; }
.icon-user:before { content: "\e608"; }
.icon-temp:before { content: "\e609"; }
.icon-sample:before { content: "\e60a"; }
.icon-shim:before { content: "\e60b"; }

.experiment-queue {
  margin-top: 20rpx;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.queue-count {
  font-size: 24rpx;
  color: #95a5a6;
  background: rgba(52, 152, 219, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.experiment-item {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-left: 8rpx solid transparent;
  
  &.current {
    border-left-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
  }
  
  &.queued {
    border-left-color: #95a5a6;
  }
}

.experiment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.experiment-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #2c3e50;
}

.experiment-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  
  &.running {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
  }
  
  &.queued {
    background: rgba(149, 165, 166, 0.1);
    color: #7f8c8d;
  }
}

.experiment-info {
  display: flex;
  gap: 24rpx;
  font-size: 24rpx;
  color: #7f8c8d;
}

.info-item {
  display: flex;
  align-items: center;
  
  .iconfont {
    font-size: 24rpx;
    margin-right: 6rpx;
  }
}

.queue-empty {
  text-align: center;
  padding: 40rpx;
  color: #95a5a6;
  font-size: 28rpx;
}

.action-btnx{
    display: flex;
    flex-direction: column;
    text-align: center;
}
.action-btnx image {
	margin: 0rpx auto;
}