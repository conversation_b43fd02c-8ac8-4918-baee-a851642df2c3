<view class="temp-gauge">
  <view class="temp-info">
    <!-- <view class="temp-icon {{tempStatus}}">
    </view> -->
    <view class="temp-details">
      <text class="temp-label">{{title}}</text>
      <view class="temp-value-section">
        <text class="temp-value">{{currentTemp}}</text>
        <text class="temp-unit">{{unit}}</text>
      </view>
    </view>
  </view>
  
  <!-- <view class="temp-bar">
    <view class="temp-bar-fill {{tempStatus}}" style="width: {{(currentTemp - minTemp) / (maxTemp - minTemp) * 100}}%"></view>
  </view> -->
  
  <!-- <view class="temp-range">
    <text>Target: 30°C</text>
    <text>Rate: 1°C/min</text>
  </view> -->
</view> 