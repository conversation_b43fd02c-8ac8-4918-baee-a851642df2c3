<!-- <view class="container">
  <view class="header">
    <image class="logo" src="/images/logo.png"></image>
    <text class="title">欢迎使用小程序</text>
  </view>
  
  <view class="user-info" wx:if="{{hasUserInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>
  
  <view class="btn-area">
    <button wx:if="{{!hasUserInfo && canIUseGetUserProfile}}" class="login-btn" bindtap="getUserProfile">获取用户信息</button>
    <button wx:if="{{!app.globalData.token}}" class="login-btn" bindtap="login">微信登录</button>
  </view>
</view> -->
<!-- login.wxml -->
<view class="container">
  <view class="background">
    <view class="bubble"></view>
    <view class="bubble"></view>
    <view class="bubble"></view>
    <view class="bubble"></view>
    <view class="bubble"></view>
  </view>
  
  <view class="content">
    <view class="logo">
      <image class="logo-img" src="/images/logo.png"></image>
    </view>
    <text class="title">国仪核磁设备管理系统</text>
    <button class="login-btn" bindtap="login">
      <view class="btn-content">
        <text>微信一键登录</text>
      </view>
    </button>
    <button class="home-btn" bindtap="goToHome">
      <view class="btn-content">
        <text>返回首页</text>
      </view>
    </button>
  </view>
  
  <!-- <view class="footer">
    <text>登录即表示您已同意《用户协议》和《隐私政策》</text>
  </view> -->
</view>