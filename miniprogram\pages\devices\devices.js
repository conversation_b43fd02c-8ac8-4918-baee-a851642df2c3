Page({
  data: {
    devices: [],
    deviceList: [],
    currentLanguage: 'zh',
    texts: {}
  },

  onLoad() {
    this.initLanguage();
  },

  // 初始化语言
  initLanguage() {
    const app = getApp();
    this.setData({
      currentLanguage: app.globalData.language,
      texts: this.getTexts(app.globalData.language)
    });
  },

  // 获取当前语言的文本
  getTexts(lang) {
    const app = getApp();
    return app.globalData.i18n[lang];
  },

  // 语言变化回调
  onLanguageChange() {
    this.initLanguage();
  },

  // 切换语言
  switchLanguage() {
    const app = getApp();
    const newLang = app.globalData.language === 'zh' ? 'en' : 'zh';
    app.switchLanguage(newLang);
    this.initLanguage();
  },

  onShow() {
    const token = wx.getStorageSync('token');
    if (token) {
      // 查询用户的设备列表
      this.fetchDeviceList();
    }
  },

  fetchDeviceList: function () {
    wx.request({
      url: `https://qspinmini.ciqtek.com/api/user/devices`,
      // url: `http://127.0.0.1:8060/api/user/devices`, 
      method: 'GET',
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
      },
      success: (res) => {
        console.log(res.data.data.devices)
        this.setData({
          devices: res.data.data.devices
        });
        wx.setStorageSync('devices', res.data.data.devices)

      },
      fail: (err) => {
        // wx.showToast({
        //   title: '请求失败，请重试',
        //   icon: 'none'
        // });
      }
    });
  },

  onScan() {
    const token = wx.getStorageSync('token');
    console.log(token)  
    if (token === '') {
      wx.navigateTo({
        url: '/pages/login/login',
      });
    } else {
      // 调用微信小程序的扫码 API
      wx.scanCode({
        success: (res) => {
          console.log('扫描结果:', res);
          const deviceId = res.result
          console.log('设备ID:', deviceId);
          wx.showToast({
            title: deviceId,
            icon: 'none',
          });
          // 执行设备绑定操作
          this.bindDevice(deviceId);
        },
        fail: (err) => {
          console.log(err)
          wx.showToast({
            title: this.data.texts.scanFailed,
            icon: 'none',
          });
        }
      });
    }
  },

  // 发送请求到后台绑定设备
  bindDevice: function (deviceId) {
    if (!deviceId) {
      wx.showToast({
        title: this.data.texts.invalidDeviceId,
        icon: 'none',
      });
      return;
    }
    const that = this;
    // 向后台发送设备绑定请求
    wx.request({
      // url: 'http://127.0.0.1:8060/api/user/bind_device', 
      url: 'https://qspinmini.ciqtek.com/api/user/bind_device',
      method: 'POST',
      data: {
        device_id: deviceId
      },
      header: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
      },
      success: (res) => {
        wx.showToast({
          title: res.data.code,
          icon: 'none',
        });
        if (res.data.code === 400) {
          wx.showToast({
            title: res.data.msg,
            icon: 'none',
          });
        } else {
          if (res.data.code === 0) {
            wx.showToast({
              title: that.data.texts.deviceBindSuccess,
              icon: 'success',
            });
            wx.setStorageSync('deviceId', deviceId)
            that.fetchDeviceList();
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: err,
          icon: 'none'
        });
      }
    });
  },

  // 跳转至设备详情页
  viewDevice(e) {
    const deviceId = e.currentTarget.dataset.id;
    wx.setStorageSync('deviceId', deviceId)
    wx.switchTab({
      url: '/pages/home/<USER>',
    });
  },

  // 解除绑定设备
  unbindDevice(e) {
    const deviceId = e.currentTarget.dataset.id;
    const that = this
    wx.showModal({
      title: this.data.texts.unbindConfirmTitle,
      content: `${this.data.texts.unbindConfirmContent} ${deviceId} 吗？`,
      success(res) {
        if (res.confirm) {
          wx.request({
            // url: 'http://127.0.0.1:8060/api/user/unbind_device',
            url: 'https://qspinmini.ciqtek.com/api/user/unbind_device',
            method: 'POST',
            data: {
              device_id: deviceId
            },
            header: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
            },
            success: (res) => {
              console.log(res.data)
              if (res.data.code === 0) {
                wx.showToast({
                  title: that.data.texts.deviceUnbindSuccess,
                  icon: 'success',
                });
                wx.setStorageSync('deviceId', '')
                that.fetchDeviceList();
              }
            }
          });

        }
      }
    });
  }
});