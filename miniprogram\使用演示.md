# 全局语言切换功能使用演示

## 功能展示

### 1. 默认状态（中文）
- 打开小程序，默认显示中文界面
- 切换到Help页面，右上角显示"EN"按钮，表示可以切换到英文

### 2. 全局切换到英文
- 在Help页面点击右上角的"EN"按钮
- 所有页面（Home、Devices、Help）的文本立即切换为英文
- 按钮文字变为"中"，表示可以切换回中文
- TabBar底部导航栏也会同步更新为英文
- 切换到其他页面，文本也都是英文状态

### 3. 全局切换回中文
- 在Help页面点击右上角的"中"按钮
- 所有页面的文本立即切换为中文
- 按钮文字变为"EN"
- 所有页面保持中文状态

## 支持的界面元素

### Home页面：
**有设备状态下：**
- 设备名称标题
- 液氦/液氮监控标题和状态
- 液位监控部分
- 检测记录部分
- 状态更新时间显示

**无设备状态下：**
- "未检测到绑定设备"提示
- 设备绑定说明文字
- "立即绑定设备"按钮
- 功能特点介绍（实时监控、智能告警、数据存储、数据共享）
- 设备绑定步骤说明

### Devices页面：
- "设备管理"标题
- 设备数量显示（"X个设备" / "X devices"）
- "查看设备"和"解除绑定"按钮
- "绑定新设备"按钮
- 扫码失败、设备绑定成功等提示信息
- 解除绑定确认对话框

### Help页面：
- "常见问题"标题
- FAQ问答内容（5个常见问题及答案）
- "售后支持"标题
- "电话支持"和"邮件支持"卡片
- 服务时间说明
- 反馈提示信息

### 全局元素：
- 导航栏标题
- 底部TabBar（首页、设备、帮助）
- 所有错误和成功提示信息

## 技术特点

1. **持久化存储**：语言选择会保存到本地，下次打开应用时会记住用户的选择

2. **实时更新**：切换语言后，当前页面的所有文本会立即更新，无需刷新

3. **全局同步**：TabBar和导航栏也会同步更新语言

4. **优雅降级**：如果某个文本没有翻译，会显示原始的key值，不会导致应用崩溃

## 扩展性

- 语言包集中管理在app.js中，便于维护
- 可以轻松添加更多语言支持
- 其他页面可以参考home页面的实现方式添加多语言支持
