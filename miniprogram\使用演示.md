# 语言切换功能使用演示

## 功能展示

### 1. 默认状态（中文）
- 打开小程序，默认显示中文界面
- 右上角显示"EN"按钮，表示可以切换到英文

### 2. 切换到英文
- 点击右上角的"EN"按钮
- 页面所有文本立即切换为英文
- 按钮文字变为"中"，表示可以切换回中文
- TabBar底部导航栏也会同步更新为英文

### 3. 切换回中文
- 点击右上角的"中"按钮
- 页面所有文本立即切换为中文
- 按钮文字变为"EN"

## 支持的界面元素

### 有设备状态下：
- 设备名称标题
- 液氦/液氮监控标题和状态
- 液位监控部分
- 检测记录部分
- 状态更新时间显示

### 无设备状态下：
- "未检测到绑定设备"提示
- 设备绑定说明文字
- "立即绑定设备"按钮
- 功能特点介绍（实时监控、智能告警、数据存储、数据共享）
- 设备绑定步骤说明

### 全局元素：
- 导航栏标题
- 底部TabBar（首页、设备、帮助）
- 错误提示信息

## 技术特点

1. **持久化存储**：语言选择会保存到本地，下次打开应用时会记住用户的选择

2. **实时更新**：切换语言后，当前页面的所有文本会立即更新，无需刷新

3. **全局同步**：TabBar和导航栏也会同步更新语言

4. **优雅降级**：如果某个文本没有翻译，会显示原始的key值，不会导致应用崩溃

## 扩展性

- 语言包集中管理在app.js中，便于维护
- 可以轻松添加更多语言支持
- 其他页面可以参考home页面的实现方式添加多语言支持
