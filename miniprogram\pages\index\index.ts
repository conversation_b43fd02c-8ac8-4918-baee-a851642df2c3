interface Device {
	id: string;
	name: string;
	isRunning: boolean;
}

interface DeviceStatus {
	helium_level?: number;
	nitrogen_level?: number;
	[key: string]: any;
}

interface FaqItem {
	question: string;
	answer: string;
}

interface ContactItem {
	title: string;
	content: string;
}

Page({
	data: {
		deviceList: [] as Device[],
		selectedDevice: null as Device | null,
		currentStatus: null as DeviceStatus | null,
		ws: null as WechatMiniprogram.SocketTask | null,
		statuses: {} as Record<string, any>,
		activeTab: 'status',
		statusBarHeight: 0,
		deviceId: 'INST-001',
		faqList: [
			{
				question: '如何预约使用核磁共振仪？',
				answer: '请登录实验室管理系统，选择"设备预约"模块，选择需要使用的设备和时间段进行预约。预约成功后，系统会发送确认邮件。'
			},
			{
				question: '液氦或液氮不足时如何处理？',
				answer: '当液氦或液氮低于30%时，系统会自动通知管理员安排补充。如有紧急需求，请联系实验室管理员张老师（电话：123-4567-8901）。'
			},
			{
				question: '实验数据如何导出？',
				answer: '实验完成后，数据会自动保存在系统中。您可以在"数据管理"模块中找到您的实验数据，点击"导出"按钮，选择需要的格式（如CSV、Excel等）进行导出。'
			}
		] as FaqItem[],
		contactList: [
			{
				title: '技术支持',
				content: '工作时间：周一至周五 9:00-17:00<br>电话：010-12345678<br>邮箱：<EMAIL>'
			},
			{
				title: '紧急情况',
				content: '如遇设备故障或紧急情况，请立即联系：<br>值班电话：15678901234（24小时）'
			}
		] as ContactItem[]
	},
	onLoad() {
		this.drawGauges();
		const systemInfo = wx.getSystemInfoSync();
		this.setData({
			statusBarHeight: systemInfo.statusBarHeight
		});
		this.connectWebSocket(); 
	},

	connectWebSocket() {
		if (this.data.ws) {
			this.data.ws.close({
				code: 1000,
				reason: '重新连接'
			});
		}

		console.log('开始建立WebSocket连接...');
		const ws = wx.connectSocket({
			url: 'ws://127.0.0.1:8060/ws/subscriber',
			// url: 'wss://qspinmini.ciqtek.com/ws/subscriber',
			success: () => {
				console.log('WebSocket连接成功');
			},
			fail: (err) => {
				console.error('WebSocket连接失败', err);
				wx.showToast({
					title: '连接失败，请检查网络',
					icon: 'none'
				});
			},
			complete: () => {
				console.log('WebSocket连接完成');
			}
		});

		wx.onSocketOpen(() => {
			console.log('WebSocket连接已打开，准备发送订阅消息...');
			const subscribeMsg = {
				instrument_id: this.data.deviceId,
				action: 'subscribe'
			};
			console.log('发送订阅消息:', subscribeMsg);
			
			wx.sendSocketMessage({
				data: JSON.stringify(subscribeMsg),
				success: () => {
					console.log('订阅消息发送成功');
				},
				fail: (err) => {
					console.error('订阅消息发送失败', err);
				}
			});
		});

		wx.onSocketMessage((res) => {
			console.log('收到WebSocket消息:', res);
			try {
				const data = JSON.parse(res.data as string) as DeviceStatus;
				console.log('解析后的设备状态数据:', data);
				
				this.setData({
					currentStatus: data
				});

				if (data.helium_level !== undefined || data.nitrogen_level !== undefined) {
					this.drawGauges();
				}
			} catch (err) {
				console.error('解析WebSocket消息失败', err);
			}
		});

		wx.onSocketError((err) => {
			console.error('WebSocket错误', err);
			wx.showToast({
				title: '连接出错，正在重连...',
				icon: 'none'
			});
			setTimeout(() => {
				this.connectWebSocket();
			}, 3000);
		});

		wx.onSocketClose((res) => {
			console.log('WebSocket连接已关闭', res);
			setTimeout(() => {
				this.connectWebSocket();
			}, 3000);
		});

		this.setData({ ws });
	},

	switchTab(e: WechatMiniprogram.CustomEvent) {
		const tab = e.currentTarget.dataset.tab;
		this.setData({
			activeTab: tab
		});

		if (tab === 'status') {
			this.drawGauges();
		}
	},

	drawGauges() {
		const { currentStatus } = this.data;
		const heliumLevel = currentStatus?.helium_level || 85;
		const nitrogenLevel = currentStatus?.nitrogen_level || 72;

		setTimeout(() => {
			this.drawGauge('heliumCanvas', heliumLevel, '#2ecc71');
			this.drawGauge('nitrogenCanvas', nitrogenLevel, '#f1c40f');
		}, 100);
	},

	drawGauge(canvasId: string, percentage: number, color: string) {
		const query = wx.createSelectorQuery();
		query.select('#' + canvasId)
			.fields({ node: true, size: true })
			.exec((res) => {
				if (!res[0] || !res[0].node) return;

				const canvas = res[0].node;
				const ctx = canvas.getContext('2d');

				const width = res[0].width;
				const height = res[0].height;
				const centerX = width / 2;
				const centerY = height / 2;
				const radius = Math.min(width, height) / 2 - 5;

				canvas.width = width;
				canvas.height = height;

				ctx.beginPath();
				ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
				ctx.lineWidth = 10;
				ctx.strokeStyle = '#eee';
				ctx.stroke();

				const startAngle = -0.5 * Math.PI;
				const endAngle = startAngle + (percentage / 100) * 2 * Math.PI;

				ctx.beginPath();
				ctx.arc(centerX, centerY, radius, startAngle, endAngle);
				ctx.lineWidth = 10;
				ctx.strokeStyle = color;
				ctx.stroke();
			});
	},

	viewDeviceDetails(e: WechatMiniprogram.CustomEvent) {
		const deviceId = e.currentTarget.dataset.device;
		wx.showToast({
			title: '查看设备详情: ' + deviceId,
			icon: 'none'
		});
	}
});