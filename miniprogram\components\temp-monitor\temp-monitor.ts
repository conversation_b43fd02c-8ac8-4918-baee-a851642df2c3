Component({
  properties: {
    title: {
      type: String,
      value: 'Temperature Monitor'
    },
    unit: {
      type: String,
      value: '°C'
    }
  },

  data: {
    currentTemp: '30.0',
    minTemp: 29.8,
    maxTemp: 30.2,
    tempStatus: 'normal',
    tempHistory: [] as number[],
    timer: null as any
  },

  lifetimes: {
    attached() {
      this.startTempSimulation();
    },
    detached() {
      if (this.data.timer) {
        clearInterval(this.data.timer);
      }
    }
  },

  methods: {
    startTempSimulation() {
      // 生成随机温度值（在30±0.2范围内）
      const generateRandomTemp = () => {
        const baseTemp = 30.0;
        const variation = 0.2;
        return baseTemp + (Math.random() * 2 - 1) * variation;
      };

      // 更新温度状态
      const updateTempStatus = (temp: number) => {
        if (temp >= 29.8 && temp <= 30.2) {
          return 'normal';
        } else if (temp >= 29.5 && temp <= 30.5) {
          return 'warning';
        } else {
          return 'danger';
        }
      };

      // 每秒更新一次温度
      const timer = setInterval(() => {
        const newTemp = generateRandomTemp();
        const status = updateTempStatus(newTemp);
        
        // 更新温度历史记录（保留最近10个值）
        const history = [...this.data.tempHistory, newTemp].slice(-10);

        this.setData({
          currentTemp: newTemp.toFixed(1),
          tempStatus: status,
          tempHistory: history
        });
      }, 1000);

      this.setData({ timer });
    }
  }
}); 