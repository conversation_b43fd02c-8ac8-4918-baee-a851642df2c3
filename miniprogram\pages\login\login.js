const app = getApp();
Page({
  data: {
    hasUserInfo: false,
    userInfo: null,
  },

  onUnload: function () {
    // 在页面卸载时恢复 tabBar
    wx.showTabBar();
  },

  onLoad: function () {
    wx.hideTabBar();
    // 检查是否已登录
    if (app.globalData.token) {
      this.setData({
        hasUserInfo: app.globalData.userInfo != null,
        userInfo: app.globalData.userInfo
      });
      console.log('已登录，用户信息:', app.globalData.userInfo);
      wx.switchTab({
        url: '/pages/home/<USER>',
      });

      // 如果已登录但没有用户信息，可以尝试获取用户信息
      // if (!this.data.hasUserInfo && app.globalData.token) {
      //   this.getUserProfile();
      // }

    }
  },

  // 执行登录
  login: function () {
    wx.showLoading({
      title: '登录中...',
    });

    app.login().then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 登录成功后可以跳转到其他页面
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/home/<USER>',
        });
      }, 1500);
    }).catch(err => {
      wx.hideLoading();
      console.error('登录失败', err);
    });
  },

  // 获取用户信息
  getUserProfile: function () {
    if (!this.data.canIUseGetUserProfile) {
      wx.showToast({
        title: '当前微信版本过低，请升级',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '获取信息中...',
    });

    app.getUserProfile().then(userInfo => {
      wx.hideLoading();
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }).catch(err => {
      wx.hideLoading();
      console.error('获取用户信息失败', err);
    });
  },

  // 返回首页
  goToHome: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
})