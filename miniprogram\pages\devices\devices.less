.container {
    padding: 16px;
    background-color: #f5f7fa;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    padding-bottom: 80px;
  }

  /* Language Switch Button */
  .language-switch-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .language-switch {
    background-color: #4F46E5;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
  }

  .language-switch:hover {
    background-color: #4338CA;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
  }

  .language-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .title {
    font-size: 20px;
    font-weight: bold;
    color: #1F2937;
  }
  
  .device-count {
    font-size: 14px;
    color: #6B7280;
  }
  
  .count-text {
    color: #3B82F6;
    font-weight: 500;
  }
  
  .device-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .device-card {
    background-color: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }
  
  .device-card:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }
  
  .device-info {
    flex: 1;
  }
  
  .device-name {
    font-size: 16px;
    font-weight: 600;
    color: #1F2937;
  }
  
  .device-status {
    display: flex;
    align-items: center;
    margin-top: 4px;
  }
  
  .status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
  }
  
  .status-indicator.online {
    background-color: #10B981;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
  }
  
  .status-indicator.offline {
    background-color: #EF4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
  }
  
  .status-indicator.warning {
    background-color: #F59E0B;
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
  }
  
  .status-text {
    font-size: 12px;
    color: #6B7280;
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
  }
  
  .unbind-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #FEE2E2;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    line-height: 1;
  }
  
  .device-details {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
  }
  
  .detail-item {
    flex: 1;
  }
  
  .detail-label {
    font-size: 12px;
    color: #6B7280;
    display: block;
  }
  
  .detail-value {
    font-size: 14px;
    font-weight: 500;
    color: #1F2937;
    display: block;
    margin-top: 4px;
  }
  
  .signal-strength {
    display: flex;
    align-items: center;
    margin-top: 4px;
  }
  
  .signal-text {
    font-size: 14px;
    font-weight: 500;
    color: #1F2937;
    margin-left: 4px;
  }
  
  .device-actions {
    display: flex;
    margin-top: 16px;
    gap: 8px;
  }
  
  .view-btn, .settings-btn {
    flex: 1;
    height: 40px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
  }
  
  .view-btn {
    background-color: #EFF6FF;
    color: #3B82F6;
  }
  
  .settings-btn {
    background-color: #F3F4F6;
    color: #6B7280;
  }
  
  .view-btn icon, .settings-btn icon {
    margin-right: 8px;
  }
  
  .bind-btn {
    position: fixed;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 32px);
    max-width: 448px;
    height: 48px;
    background-color: #3B82F6;
    color: white;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
  
  .bind-btn:active {
    background-color: #2563EB;
  }
  
  .bind-btn icon {
    margin-right: 8px;
  }