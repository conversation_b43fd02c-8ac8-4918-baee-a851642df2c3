# 全局语言切换功能测试说明

## 测试目标
验证从Help页面的语言切换按钮能够正确地切换所有页面的语言，确保全局同步功能正常工作。

## 测试步骤

### 1. 初始状态测试
1. 打开小程序
2. 检查默认语言是否为中文
3. 浏览Home、Devices、Help三个页面
4. 确认所有页面都显示中文文本
5. 确认TabBar显示"首页"、"设备"、"帮助"

### 2. 切换到英文测试
1. 切换到Help页面
2. 点击右上角的"EN"按钮
3. 验证Help页面立即切换为英文
4. 切换到Home页面，验证文本已切换为英文
5. 切换到Devices页面，验证文本已切换为英文
6. 确认TabBar显示"Home"、"Devices"、"Help"
7. 确认Help页面按钮显示"中"

### 3. 切换回中文测试
1. 在Help页面点击"中"按钮
2. 验证Help页面立即切换为中文
3. 切换到其他页面，验证都已切换为中文
4. 确认TabBar恢复显示中文
5. 确认Help页面按钮显示"EN"

### 4. 持久化测试
1. 切换语言到英文
2. 关闭小程序
3. 重新打开小程序
4. 验证语言设置是否保持为英文
5. 验证所有页面都显示英文

### 5. 页面间切换测试
1. 在Help页面切换语言
2. 快速在三个页面间切换
3. 验证每个页面的语言都保持一致
4. 验证没有出现语言混乱的情况

## 预期结果

### 功能正常的标志：
- ✅ 只有Help页面有语言切换按钮
- ✅ 点击按钮后所有页面立即同步更新
- ✅ TabBar文本同步更新
- ✅ 语言设置持久化保存
- ✅ 页面间切换时语言保持一致
- ✅ 错误提示信息也支持多语言

### 需要检查的文本内容：

#### Home页面：
- 设备名称、液氦、液氮等监控文本
- 无设备时的提示和绑定引导文本
- 功能特点介绍文本

#### Devices页面：
- 设备管理标题
- 设备数量显示
- 查看设备/解除绑定按钮
- 绑定新设备按钮
- 操作提示信息

#### Help页面：
- 常见问题标题
- FAQ问答内容
- 售后支持相关文本
- 联系方式说明

## 故障排除

如果发现问题：
1. 检查app.js中的switchLanguage方法是否正确调用updateTabBar
2. 检查各页面的onLanguageChange方法是否正确实现
3. 检查语言包中是否有遗漏的翻译文本
4. 检查本地存储是否正常工作

## 优势说明

这种全局语言切换方案的优势：
1. **用户体验统一** - 只需在一个地方切换语言
2. **维护简单** - 不需要在每个页面都维护语言切换按钮
3. **逻辑清晰** - 语言切换逻辑集中在Help页面
4. **全局同步** - 确保所有页面语言状态一致
5. **符合惯例** - 语言设置通常放在设置或帮助页面
