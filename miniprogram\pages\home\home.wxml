<view class="container" wx:if="{{devices.length > 0}}">

  <!-- Device Status -->
  <view class="status-card">
    <view class="flex justify-between items-center mb-3">
      <view class="section-title-container">
        <text class="section-title">{{texts.deviceName}} · {{device_name}}</text>
        <view class="edit-btn" bindtap="showEditDeviceName">
          <image src='../../images/edit.png' style="width:32rpx;height:32rpx" />
        </view>
      </view>
      <view class="flex items-center">
        <view class="status-dot {{currentStatus.status.status !== 'offline' ? 'green' : 'yellow'}}"></view>
        <view class="status-text {{currentStatus.status.status !== 'offline' ? 'green' : 'yellow'}}">
          {{currentStatus.status.status}}
        </view>
      </view>
    </view>
    
    <view class="grid grid-cols-2 gap-4">
      <view class="info-card blue" style="width: 100%;">
        <view class="flex items-center mb-1">
          <text class="info-label">Exp Name：{{currentStatus.status.exp_name}}</text>
        </view>
        <view class="flex items-center mb-1">
          <text class="info-label">Solvent：{{currentStatus.status.solvent}}</text>
        </view>
        <view class="flex items-center mb-1">
          <text class="info-label">Sequence：{{currentStatus.status.sequence}}</text>
        </view>
        <view class="flex items-center mb-1">
          <text class="info-label">Progress：{{currentStatus.status.progress}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- Temperature Monitoring -->
  <view class="status-card {{currentStatus.status.status === 'offline' ? 'disabled' : ''}}">
    <view class="flex justify-between items-center mb-3">
      <view class="section-title-container">
        <text class="section-title">{{texts.TemperatureMonitoring}}</text>
      </view>
      <!-- <view class="flex items-center">
        <view class="status-dot green"></view>
        <text class="status-text green">正常</text>
      </view> -->
    </view>
    
    <view class="grid grid-cols-2 gap-4">
      <view class="info-card red">
        <view class="flex items-center mb-1">
          <icon type="info" size="16" color="#636060"></icon>
          <text class="info-label">Sample</text>
        </view>
        <text class="info-value">{{currentStatus.status.sample_temp}}°C</text>
        <view class="gauge bg-gray mt-2">
          <view class="gauge-fill blue" style="width: {{currentStatus.status.sample_temp}}%"></view>
        </view>
        <!-- <view class="flex justify-between text-xs text-gray mt-1">
          <text>-150°C</text>
          <text>0°C</text>
          <text>150°C</text>
        </view> -->
      </view>
      
      <view class="info-card orange">
        <view class="flex items-center mb-1">
          <icon type="info" size="16" color="#F59E0B"></icon>
          <text class="info-label">Shim</text>
        </view>
        <text class="info-value">{{currentStatus.status.shim_temp}}°C</text>
        <view class="gauge bg-gray mt-2">
          <view class="gauge-fill orange" style="width: {{currentStatus.status.shim_temp}}%"></view>
        </view>
        <!-- <view class="flex justify-between text-xs text-gray mt-1">
          <text>0°C</text>
          <text>50°C</text>
          <text>100°C</text>
        </view> -->
      </view>
    </view>
  </view>
  
  <!-- Liquid Level Monitoring -->
  <view class="status-card {{currentStatus.status.status === 'offline' ? 'disabled' : ''}}">
    <view class="flex justify-between items-center mb-3">
      <view class="section-title-container">
        <text class="section-title">{{texts.liquidLevelMonitoring}}</text>
      </view>
      <!-- <view class="flex items-center">
        <view class="status-dot green"></view>
        <text class="status-text green">正常</text>
      </view> -->

      <!-- <view class="flex items-center">
        <view class="status-dot yellow"></view>
        <text class="status-text yellow">警告</text>
      </view> -->
    </view>
    
    <view class="grid grid-cols-2 gap-4">
      <view class="info-card blue">
        <view class="flex items-center mb-1">
          <icon type="info" size="16" color="#60A5FA"></icon>
          <text class="info-label">{{texts.liquidNitrogen}}</text>
        </view>
        <text class="info-value">{{currentStatus.status.nitrogen_level}}%</text>
        <view class="relative h-2 bg-gray rounded-full overflow-hidden mt-2">
          <view class="absolute top-0 left-0 h-full bg-blue rounded-full" style="width: {{currentStatus.status.nitrogen_level}}%"></view>
        </view>
        <view class="flex justify-between text-xs text-gray mt-1">
          <text>{{texts.empty}}</text>
          <text>50%</text>
          <text>{{texts.full}}</text>
        </view>
      </view>

      <view class="info-card indigo">
        <view class="flex items-center mb-1">
          <icon type="info" size="16" color="#818CF8"></icon>
          <text class="info-label">{{texts.liquidHelium}}</text>
        </view>
        <text class="info-value {{currentStatus.status.helium_level ? 'blink' : ''}}">{{currentStatus.status.helium_level}}%</text>
        <view class="relative h-2 bg-gray rounded-full overflow-hidden mt-2">
          <view class="absolute top-0 left-0 h-full bg-indigo rounded-full" style="width: {{currentStatus.status.helium_level}}%"></view>
        </view>
        <view class="flex justify-between text-xs text-gray mt-1">
          <text>{{texts.empty}}</text>
          <text>50%</text>
          <text>{{texts.full}}</text>
        </view>
        <!-- <view class="text-xs text-yellow mt-1 flex items-center" wx:if="{{heliumWarning}}">
          <icon type="warn" size="12" color="#F59E0B"></icon>
          <text>液氦不足，请及时补充</text>
        </view> -->
      </view>
    </view>
  </view>
  
  <!-- Update Time -->
  <view class="status-card {{currentStatus.status.status === 'offline' ? 'disabled' : ''}}">
    <view class="flex justify-between items-center mb-3">
      <view class="section-title-container">
        <text class="section-title">{{texts.detectionRecords}}</text>
      </view>
      <view class="flex items-center">
        <icon type="info" size="16" color="#60A5FA"></icon>
        <text class="status-text blue">{{texts.recentlyUpdated}}</text>
      </view>
    </view>
    
    <view class="update-time-container">
      <view class="update-time-item">
        <view class="update-time-header">
          <icon type="info" size="16" color="#818CF8"></icon>
          <text class="update-time-label">{{texts.heliumStatusUpdate}}</text>
        </view>
        <text class="update-time-value">{{currentStatus.status.helium_date || texts.noRecords}}</text>
      </view>

      <view class="update-time-divider"></view>

      <view class="update-time-item">
        <view class="update-time-header">
          <icon type="info" size="16" color="#60A5FA"></icon>
          <text class="update-time-label">{{texts.nitrogenStatusUpdate}}</text>
        </view>
        <text class="update-time-value">{{currentStatus.status.nitrogen_date || texts.noRecords}}</text>
      </view>
    </view>
  </view>
  
</view>


<view class="device-container" wx:if="{{devices.length === 0}}">
  <!-- Language Switch Button -->
  <view class="language-switch-container">
    <view class="language-switch" bindtap="switchLanguage">
      <text class="language-text">{{currentLanguage === 'zh' ? 'EN' : '中'}}</text>
    </view>
  </view>

  <!-- 设备图标 -->
  <!-- <view class="device-icon">
    <view class="device-icon-inner">
      <view class="icon-bg">
        <text class="iconfont icon-microscope"></text>
      </view>
      <view class="warning-badge">
        <text class="iconfont icon-exclamation"></text>
      </view>
    </view>
  </view> -->

  <!-- 标题和描述 -->
  <view class="title">{{texts.noDeviceDetected}}</view>
  <view class="description">{{texts.noDeviceDescription}}</view>

  <!-- 绑定按钮 -->
  <button class="bind-btn" bindtap="bindDevice">
    <image class="mr-2" src='../../images/link.png' style="width:30rpx;height:30rpx" />{{texts.bindDeviceNow}}
  </button>
  
  <!-- 分割线 -->
  <view class="divider">
    <view class="divider-line"></view>
    <view class="divider-text">{{texts.afterBinding}}</view>
  </view>

  <!-- 功能特点 -->
  <view class="feature-grid">
    <view class="feature-card" bindtap="showFeatureInfo" data-title="Real-time Monitoring">
      <view class="feature-icon-bg blue">
        <image src='../../images/status.png' style="width:40rpx;height:40rpx" />
      </view>
      <view class="feature-title">{{texts.realtimeMonitoring}}</view>
      <view class="feature-desc">{{texts.realtimeMonitoringDesc}}</view>
    </view>

    <view class="feature-card" bindtap="showFeatureInfo" data-title="Smart Alerts">
      <view class="feature-icon-bg green">
        <image src='../../images/notice.png' style="width:50rpx;height:50rpx" />
      </view>
       <view class="feature-title">{{texts.smartAlerts}}</view>
      <view class="feature-desc">{{texts.smartAlertsDesc}}</view>
    </view>

    <view class="feature-card" bindtap="showFeatureInfo" data-title="Data Storage">
      <view class="feature-icon-bg purple">
        <image src='../../images/data.png' style="width:50rpx;height:50rpx" />
      </view>
      <view class="feature-title">{{texts.dataStorage}}</view>
      <view class="feature-desc">{{texts.dataStorageDesc}}</view>
    </view>

    <view class="feature-card" bindtap="showFeatureInfo" data-title="Data Sharing">
      <view class="feature-icon-bg yellow">
        <image src='../../images/share.png' style="width:40rpx;height:40rpx" />
      </view>
      <view class="feature-title">{{texts.dataSharing}}</view>
      <view class="feature-desc">{{texts.dataSharingDesc}}</view>
    </view>
  </view>

  <!-- 绑定步骤 -->
  <view class="bind-steps">
    <view class="steps-title">
      <image src='../../images/tip.png' style="width:36rpx;height:36rpx;margin-right: 10rpx;" />
      <text>{{texts.deviceBindingSteps}}</text>
    </view>
    <view class="steps-list">
      <view class="step-item">
        <view class="step-number">1</view>
        <view class="step-text">{{texts.step1}}</view>
      </view>
      <view class="step-item">
        <view class="step-number">2</view>
        <view class="step-text">{{texts.step2}}</view>
      </view>
      <view class="step-item">
        <view class="step-number">3</view>
        <view class="step-text">{{texts.step3}}</view>
      </view>
      <view class="step-item">
        <view class="step-number">4</view>
        <view class="step-text">{{texts.step4}}</view>
      </view>
    </view>
  </view>
  
  <!-- 帮助链接 -->
  <!-- <view class="help-links">
    <text>遇到问题？</text>
    <text class="link" bindtap="showHelp">查看帮助文档</text>
    <text>或</text>
    <text class="link" bindtap="contactService">联系客服</text>
  </view> -->
</view>