/* login.wxss */
page {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #1a2a3a, #20466b);
}

.container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.bubble {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.bubble:nth-child(1) {
  width: 80px;
  height: 80px;
  left: 10%;
  top: 20%;
  animation: float 8s infinite ease-in-out;
}

.bubble:nth-child(2) {
  width: 120px;
  height: 120px;
  right: 15%;
  top: 30%;
  animation: float 9s infinite ease-in-out 1s;
}

.bubble:nth-child(3) {
  width: 60px;
  height: 60px;
  left: 30%;
  bottom: 30%;
  animation: float 7s infinite ease-in-out 2s;
}

.bubble:nth-child(4) {
  width: 100px;
  height: 100px;
  right: 25%;
  bottom: 20%;
  animation: float 10s infinite ease-in-out 3s;
}

.bubble:nth-child(5) {
  width: 50px;
  height: 50px;
  left: 50%;
  top: 15%;
  animation: float 8s infinite ease-in-out 4s;
}

@keyframes float {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }
}

.content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
}

.logo {
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-img {
  width: 90px;
  height: 90px;
}

.title {
  color: white;
  font-size: 24px;
  margin-bottom: 60px;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.login-btn, .home-btn {
  background-color: #07c160;
  color: white;
  border: none;
  padding: 0;
  width: 240px;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 8px 16px rgba(7, 193, 96, 0.3);
  position: relative;
}

.home-btn {
  background-color: #4e4b4b;
  margin-top: 20rpx;
}

.btn-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.wechat-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.footer {
  position: absolute;
  bottom: 40px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  text-align: center;
}