<view class="container">
  <view class="content-section" wx:if="{{activeTab === 'status'}}">
    <view class="device-info-card">
      <view class="device-info-header">
        <view class="device-name-section">
          <text class="device-title">设备名称</text>
        </view>
        <view class="device-id">ID: CAN400-DEVICE_250131</view>
      </view>
      <view class="device-meta">
        <view class="meta-item">
          <text class="label">状态:</text>
          <text class="value">{{currentStatus.status.status}}</text>
        </view>
        <!-- <view class="meta-item">
          <text class="label">运行时间:</text>
          <text class="value">72小时</text>
        </view> -->
      </view>
    </view>
    <!-- <view class="status-card">
      <view class="card-title">
        <text>实验状态</text>
      </view>
      <view class="experiment-queue">
        <view class="queue-header">
          <text class="queue-count">3 个实验排队</text>
        </view>
        <view class="experiment-item current">
          <view class="experiment-header">
            <text class="experiment-name">1H sensitivity test</text>
            <text class="experiment-status running">运行中</text>
          </view>
          <view class="experiment-info">
            <view class="info-item">
              <text class="iconfont icon-time"></text>
              <text>开始时间: 2025-03-22 09:30</text>
            </view>
            <view class="info-item">
              <text class="iconfont icon-timer"></text>
              <text>预计时长: 3min</text>
            </view>
          
          </view>
        </view>
        <view class="experiment-item queued">
          <view class="experiment-header">
            <text class="experiment-name">1H lineshape</text>
            <text class="experiment-status queued">排队中</text>
          </view>
          <view class="experiment-info">
            <view class="info-item">
              <text class="iconfont icon-time"></text>
              <text>预计开始时间: 2025-03-22 10:00</text>
            </view>
            <view class="info-item">
              <text class="iconfont icon-timer"></text>
              <text>预计时长: 10min</text>
            </view>
          </view>
        </view>
        <view class="experiment-item queued">
          <view class="experiment-header">
            <text class="experiment-name">13C lineshape</text>
            <text class="experiment-status queued">排队中</text>
          </view>
          <view class="experiment-info">
            <view class="info-item">
              <text class="iconfont icon-time"></text>
              <text>预计开始时间: 2025-03-22 14:00</text>
            </view>
            <view class="info-item">
              <text class="iconfont icon-timer"></text>
              <text>预计时长: 10min</text>
            </view>
          </view>
        </view>
      </view>
    </view> -->
    <view class="status-card">
      <view class="card-title">温度监控</view>
      <view class="temp-container">
        <temp-monitor title="Sample" :value="currentStatus.status.sample_temp" unit="°C" />
        <temp-monitor title="Shim" unit="°C" />
      </view>
    </view>
    <view class="status-card">
      <view class="card-title">
        <!-- <text class="iconfont icon-status"></text> -->
        液体状态监控
      </view>
      <view class="gauges-container">
        <!-- 氦气监控 -->
        <view class="gauge">
          <view class="gauge-header">
            <!-- <view class="icon helium">
              <text class="iconfont icon-helium"></text>
            </view> -->
            <text class="gauge-title">氦气液位</text>
          </view>
          <view class="gauge-value-section">
            <text class="gauge-value">{{currentStatus.status.helium_level}}</text>
            <text class="gauge-unit">%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill helium" style="width: 85%;"></view>
          </view>
          <view class="gauge-meta">
            <!-- <view class="min-max">
              <text>最小: 20%</text>
              <text>最大: 95%</text>
            </view> -->
            <view class="status normal">正常</view>
          </view>
        </view>
        <!-- 氮气监控 -->
        <view class="gauge">
          <view class="gauge-header">
            <!-- <view class="icon nitrogen">
              <text class="iconfont icon-nitrogen"></text>
            </view> -->
            <text class="gauge-title">氮气液位</text>
          </view>
          <view class="gauge-value-section">
            <text class="gauge-value">{{currentStatus.status.nitrogen_level}}</text>
            <text class="gauge-unit">%</text>
          </view>
          <view class="progress-bar">
            <view class="progress-fill nitrogen" style="width: 72%;"></view>
          </view>
          <view class="gauge-meta">
            <!-- <view class="min-max">
              <text>最小: 15%</text>
              <text>最大: 90%</text>
            </view> -->
            <view class="status normal">正常</view>
          </view>
        </view>
      </view>
      <view class="status-item">
        <view class="status-label">液氦上次添加时间</view>
        <view class="status-value">2025-03-15</view>
      </view>
      <view class="status-item">
        <view class="status-label">液氮上次添加时间</view>
        <view class="status-value">2025-03-10</view>
      </view>
    </view>
  </view>
  <view class="content-section" wx:if="{{activeTab === 'devices'}}">
    <view class="status-card">
      <view class="card-title">
        <!-- <text class="iconfont icon-devices"></text> -->
        设备列表
      </view>
      <view class="device-list">
        <view class="device-item" wx:for="{{deviceList}}" wx:key="name">
          <view>
            <view class="device-name">{{item.name}}</view>
            <view class="device-status">
              状态:
              <view class="status-badge {{item.isRunning ? 'green' : 'red'}}">
                {{item.isRunning ? '运行中' : '维护中'}}
              </view>
            </view>
          </view>
          <view class="device-actions">
            <button class="action-btn" bindtap="viewDeviceDetails" data-device="{{item.id}}">
              查看详情
            </button>
          </view>
        </view>
        
		<view class="action-btnx">
		  <image src="./scan.png" style="width:100rpx;height: 100rpx;" alt="scan"/>
		  +扫码绑定新设备
		</view>
      </view>
    </view>
  </view>
  <view class="content-section" wx:if="{{activeTab === 'help'}}">
    <view class="status-card">
      <view class="card-title">
        <!-- <text class="iconfont icon-help"></text> -->
        常见问题
      </view>
      <view class="help-item" wx:for="{{faqList}}" wx:key="question">
        <view class="help-question">{{item.question}}</view>
        <view class="help-answer">{{item.answer}}</view>
      </view>
    </view>
    <view class="status-card">
      <view class="card-title">联系我们</view>
      <view class="help-item" wx:for="{{contactList}}" wx:key="title">
        <view class="help-question">{{item.title}}</view>
        <view class="help-answer">
          <rich-text nodes="{{item.content}}"></rich-text>
        </view>
      </view>
    </view>
  </view>
</view>
<view class="bottom-nav">
  <view class="bottom-nav-item {{activeTab === 'status' ? 'active' : ''}}" bindtap="switchTab" data-tab="status">
    <!-- <text class="iconfont icon-home"></text> -->
    <text>首页</text>
  </view>
  <view class="bottom-nav-item {{activeTab === 'devices' ? 'active' : ''}}" bindtap="switchTab" data-tab="devices">
    <!-- <text class="iconfont icon-list"></text> -->
    <text>设备列表</text>
  </view>
  <view class="bottom-nav-item {{activeTab === 'help' ? 'active' : ''}}" bindtap="switchTab" data-tab="help">
    <!-- <text class="iconfont icon-help"></text> -->
    <text>帮助</text>
  </view>
</view>