const app = getApp();

const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token') || app.globalData.token;
    
    wx.request({
      url,
      method,
      data,
      header: {
        'content-type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: res => {
        if (res.statusCode === 401) {
          // token 失效，重新登录
          app.login().then(() => {
            // 重新发起请求
            request(url, method, data).then(resolve).catch(reject);
          }).catch(err => {
            reject(err);
          });
          return;
        }
        
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          wx.showToast({
            title: res.data.msg || '请求失败',
            icon: 'none'
          });
          reject(res.data);
        }
      },
      fail: err => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
};

module.exports = {
  get: (url, data) => request(url, 'GET', data),
  post: (url, data) => request(url, 'POST', data),
  put: (url, data) => request(url, 'PUT', data),
  delete: (url, data) => request(url, 'DELETE', data)
};