Page({
	data: {
		deviceId: 'INST-001',
		selectedDevice: null,
		device_name: '',
		currentStatus: {},
		connectionStatus: 'offline',
		devices: [],
		pollingTimer: null,
		currentLanguage: 'zh',
		texts: {}
	},

	onLoad() {
		wx.showTabBar();
		this.initLanguage();
	},

	// 初始化语言
	initLanguage() {
		const app = getApp();
		this.setData({
			currentLanguage: app.globalData.language,
			texts: this.getTexts(app.globalData.language)
		});
	},

	// 获取当前语言的文本
	getTexts(lang) {
		const app = getApp();
		return app.globalData.i18n[lang];
	},

	// 语言变化回调
	onLanguageChange() {
		this.initLanguage();
	},



	onShow() {
		// 获取设备列表
		const devices = wx.getStorageSync('devices');
		console.log('devices', devices);
		this.setData({ devices });

		if (!devices || devices.length === 0) {
			console.log('没有可用设备，不启动轮询');
			this.stopPolling();
			return;
		}

		if (!wx.getStorageSync('deviceId')) {
			console.log('没有可用设备，不启动轮询');
			this.stopPolling();
			return;
		}
		this.fetchDeviceList();

		// 开始轮询
		this.startPolling();
	},

	onHide() {
		// 页面隐藏时停止轮询
		this.stopPolling();
	},

	// 开始轮询
	startPolling() {
		// 先停止之前的轮询
		this.stopPolling();
		
		// 立即执行一次查询
		this.fetchDeviceStatus();
		
		// 设置定时器，每5秒查询一次
		const timer = setInterval(() => {
			this.fetchDeviceStatus();
		}, 5000);
		
		this.setData({ pollingTimer: timer });
	},

	// 停止轮询
	stopPolling() {
		if (this.data.pollingTimer) {
			clearInterval(this.data.pollingTimer);
			this.setData({ pollingTimer: null });
		}
	},

	fetchDeviceList () {
		wx.request({
		  url: `https://qspinmini.ciqtek.com/api/user/devices`,
		  // url: `http://127.0.0.1:8060/api/user/devices`, 
	
		  method: 'GET',
		  header: {
			'Content-Type': 'application/json',
			'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
		  },
		  success: (res) => {
			console.log(res.data.data.devices[0])
			this.setData({
			  device_name: res.data.data.devices[0].device_name === null ? res.data.data.devices[0].device_id : res.data.data.devices[0].device_name
			});
		  }
		});
	  },

	// 获取设备状态
	fetchDeviceStatus() {
		const deviceId = wx.getStorageSync('deviceId');
		if (!deviceId) {
			console.log('没有设备ID，不进行查询');
			return;
		}

		wx.request({
			url: `https://qspinmini.ciqtek.com/api/device/status/${deviceId}`,
			// url: `http://127.0.0.1:8060/api/device/status/${deviceId}`,
			method: 'GET',
			success: (res) => {
				if (res.data) {
					const data = res.data.data.device;
					
					// 格式化温度和液位数据，保留一位小数
					if (data.status) {
						if (data.status.sample_temp) {
							if(Number(data.status.sample_temp) < -273.15) {
								data.status.sample_temp = '--'
							}else{
								data.status.sample_temp = Number(data.status.sample_temp).toFixed(1);
							}
						}
						if (data.status.shim_temp) {
							if(data.status.shim_temp < -273.15) {
								data.status.shim_temp = '--'
							}else{
								data.status.shim_temp = Number(data.status.shim_temp).toFixed(1);
							}
						}
						if (data.status.nitrogen_level) {
							data.status.nitrogen_level = Number(data.status.nitrogen_level).toFixed(1);
						}
						if (data.status.helium_level) {
							data.status.helium_level = Number(data.status.helium_level).toFixed(1);
						}
						if (data.status.progress) {
							if (data.status.exp_name === '--') {
								data.status.progress = '--';
							} else {
								let progress = ((data.status.exp_elapsed_time / data.status.exp_total_time ) * 100).toFixed(0);
								data.status.progress = Math.min(Number(progress), 100) + '%';
							}
						}
					}

					this.setData({
						currentStatus: data,
						connectionStatus: 'online'
					});
				}
			},
			fail: (err) => {
				console.error('获取设备状态失败:', err);
				this.setData({
					connectionStatus: 'offline',
					currentStatus: {
						status: {
							status: 'offline'
						}
					}
				});
			}
		});
	},

	bindDevice() {
		wx.switchTab({
			url: '/pages/devices/devices'
		});
	},

	// 显示编辑设备名称对话框
	showEditDeviceName() {
		wx.showModal({
			title: '修改设备名称',
			editable: true,
			placeholderText: '请输入新的设备名称',
			content: this.data.device_name,
			success: (res) => {
				if (res.confirm && res.content) {
					this.updateDeviceName(res.content);
				}
			}
		});
	},

	// 更新设备名称
	updateDeviceName(newName) {
		wx.request({
			url: 'https://qspinmini.ciqtek.com/api/user/update_device_name',
			// url: 'http://127.0.0.1:8060/api/user/update_device_name',
			method: 'POST',
			data: {
				name: newName
			},
			header: {
				'Content-Type': 'application/json',
				'Authorization': 'Bearer ' + wx.getStorageSync('token')  // 注意格式：Bearer + 空格 + token
			},
			success: (res) => {
				if (res.data.code === 0) {
					wx.showToast({
						title: this.data.texts.modifySuccess,
						icon: 'success'
					});
					console.log(res.data.data.name)

					this.setData({ device_name: res.data.data.name });
				} else {
					wx.showToast({
						title: this.data.texts.modifyFailed,
						icon: 'error'
					});
				}
			},
			fail: () => {
				wx.showToast({
					title: this.data.texts.networkError,
					icon: 'error'
				});
			}
		});
	}
});
  